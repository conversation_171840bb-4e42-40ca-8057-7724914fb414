const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function seedClients() {
  try {
    console.log('Starting client seeding...')

    // Sample client data
    const clientsData = [
      {
        companyname: 'TechCorp Solutions',
        contactname: '<PERSON>',
        contactposition: 'CTO',
        contactemail: '<EMAIL>',
        contactphone: '******-0101',
        companywebsite: 'https://techcorp.com',
        address: '123 Innovation Drive',
        city: 'San Francisco',
        state: 'CA',
        zipcode: '94105',
        country: 'USA',
        notes: 'Leading technology company specializing in AI solutions',
        isactive: true
      },
      {
        companyname: 'Global Marketing Inc',
        contactname: '<PERSON>',
        contactposition: 'Marketing Director',
        contactemail: '<EMAIL>',
        contactphone: '******-0102',
        companywebsite: 'https://globalmarketing.com',
        address: '456 Business Plaza',
        city: 'New York',
        state: 'NY',
        zipcode: '10001',
        country: 'USA',
        notes: 'Full-service marketing agency with global reach',
        isactive: true
      },
      {
        companyname: 'StartupHub Ventures',
        contactname: '<PERSON>',
        contactposition: 'Founder & CEO',
        contactemail: '<EMAIL>',
        contactphone: '******-0103',
        companywebsite: 'https://startuphub.com',
        address: '789 Startup Street',
        city: 'Austin',
        state: 'TX',
        zipcode: '73301',
        country: 'USA',
        notes: 'Venture capital firm focused on early-stage startups',
        isactive: true
      },
      {
        companyname: 'E-Commerce Plus',
        contactname: 'David Kim',
        contactposition: 'VP of Technology',
        contactemail: '<EMAIL>',
        contactphone: '******-0104',
        companywebsite: 'https://ecommerceplus.com',
        address: '321 Commerce Way',
        city: 'Seattle',
        state: 'WA',
        zipcode: '98101',
        country: 'USA',
        notes: 'Leading e-commerce platform provider',
        isactive: true
      },
      {
        companyname: 'HealthTech Innovations',
        contactname: 'Dr. Lisa Wang',
        contactposition: 'Chief Medical Officer',
        contactemail: '<EMAIL>',
        contactphone: '******-0105',
        companywebsite: 'https://healthtech.com',
        address: '654 Medical Center Blvd',
        city: 'Boston',
        state: 'MA',
        zipcode: '02101',
        country: 'USA',
        notes: 'Healthcare technology solutions for hospitals',
        isactive: true
      },
      {
        companyname: 'FinanceFlow Systems',
        contactname: 'Robert Thompson',
        contactposition: 'CFO',
        contactemail: '<EMAIL>',
        contactphone: '******-0106',
        companywebsite: 'https://financeflow.com',
        address: '987 Financial District',
        city: 'Chicago',
        state: 'IL',
        zipcode: '60601',
        country: 'USA',
        notes: 'Financial software solutions for enterprises',
        isactive: true
      },
      {
        companyname: 'Creative Design Studio',
        contactname: 'Amanda Foster',
        contactposition: 'Creative Director',
        contactemail: '<EMAIL>',
        contactphone: '******-0107',
        companywebsite: 'https://creativedesign.com',
        address: '147 Art District',
        city: 'Los Angeles',
        state: 'CA',
        zipcode: '90210',
        country: 'USA',
        notes: 'Award-winning design agency specializing in branding',
        isactive: true
      },
      {
        companyname: 'DataAnalytics Pro',
        contactname: 'James Wilson',
        contactposition: 'Data Scientist',
        contactemail: '<EMAIL>',
        contactphone: '******-0108',
        companywebsite: 'https://dataanalytics.com',
        address: '258 Data Center Road',
        city: 'Denver',
        state: 'CO',
        zipcode: '80201',
        country: 'USA',
        notes: 'Big data analytics and machine learning solutions',
        isactive: true
      },
      {
        companyname: 'CloudFirst Technologies',
        contactname: 'Maria Garcia',
        contactposition: 'Cloud Architect',
        contactemail: '<EMAIL>',
        contactphone: '******-0109',
        companywebsite: 'https://cloudfirst.com',
        address: '369 Cloud Avenue',
        city: 'Miami',
        state: 'FL',
        zipcode: '33101',
        country: 'USA',
        notes: 'Cloud migration and infrastructure services',
        isactive: true
      },
      {
        companyname: 'Legacy Systems Corp',
        contactname: 'Thomas Brown',
        contactposition: 'IT Manager',
        contactemail: '<EMAIL>',
        contactphone: '******-0110',
        companywebsite: 'https://legacysystems.com',
        address: '741 Old Tech Blvd',
        city: 'Detroit',
        state: 'MI',
        zipcode: '48201',
        country: 'USA',
        notes: 'Legacy system maintenance and modernization',
        isactive: false
      }
    ]

    console.log('Creating clients...')
    
    for (const clientData of clientsData) {
      const client = await prisma.clients.create({
        data: clientData
      })

      console.log(`Created client: ${clientData.companyname}`)

      // Create some sample orders first (required for projects)
      const ordersData = [
        {
          clientid: client.id,
          ordertitle: 'Development Package',
          orderdesc: 'Custom development package',
          ordertotalamount: 20000 + Math.random() * 30000,
          status: 'Confirmed',
          orderdate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
          createdat: new Date(Date.now() - 22 * 24 * 60 * 60 * 1000) // 22 days ago
        }
      ]

      const createdOrders = []
      for (const orderData of ordersData) {
        const order = await prisma.orders.create({
          data: orderData
        })
        createdOrders.push(order)
      }

      // Create some sample projects for each client (using the created order)
      const projectsData = [
        {
          clientid: client.id,
          orderid: createdOrders[0].id,
          name: 'Website Redesign',
          description: 'Website redesign project',
          status: 'In Progress',
          projstartdate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          estimatecost: 15000 + Math.random() * 35000,
          createdat: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000) // 25 days ago
        },
        {
          clientid: client.id,
          orderid: createdOrders[0].id,
          name: 'Mobile App',
          description: 'Mobile application development',
          status: 'Planning',
          projstartdate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
          estimatecost: 25000 + Math.random() * 50000,
          createdat: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // 15 days ago
        }
      ]

      const createdProjects = []
      for (const projectData of projectsData) {
        const project = await prisma.projects.create({
          data: projectData
        })
        createdProjects.push(project)
      }

      // Create some sample contracts (using created project and order)
      const contractsData = [
        {
          clientid: client.id,
          projid: createdProjects[0].id,
          orderid: createdOrders[0].id,
          contname: 'Service Agreement',
          contstatus: 'Active',
          contvalue: 50000 + Math.random() * 100000,
          contservtype: 'Web Development',
          contlang: 'English',
          agreementdesc: 'Web development services',
          contvaluecurr: 'USD',
          billingtype: 'Monthly',
          contsignmethod: 'Electronic Signature',
          contsigneddate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
          contexecuteddate: new Date(Date.now() - 55 * 24 * 60 * 60 * 1000), // 55 days ago
          contexpirydate: new Date(Date.now() + 305 * 24 * 60 * 60 * 1000), // ~1 year from now
          createdat: new Date(Date.now() - 65 * 24 * 60 * 60 * 1000) // 65 days ago
        }
      ]

      for (const contractData of contractsData) {
        await prisma.contracts.create({
          data: contractData
        })
      }

      // Orders already created above

      // Create some sample testimonials
      if (Math.random() > 0.3) { // 70% chance of having a testimonial
        const testimonialData = {
          clientid: client.id,
          clientname: clientData.contactname,
          clienttitle: clientData.contactposition,
          clientcompany: clientData.companyname,
          content: `Working with this team has been an exceptional experience. They delivered high-quality results on time and within budget. The communication throughout the project was excellent, and they truly understood our business needs.`,
          rating: 4 + Math.random(), // 4-5 star rating
          isfeatured: Math.random() > 0.7, // 30% chance of being featured
          createdat: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000) // Random date within last 90 days
        }

        await prisma.testimonials.create({
          data: testimonialData
        })
      }
    }

    console.log('Client seeding completed successfully!')

  } catch (error) {
    console.error('Error seeding clients:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedClients()
