const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function seedInvoices() {
  try {
    console.log('Starting invoice seeding...')

    // First, let's check if we have any clients
    const clientCount = await prisma.clients.count()
    console.log(`Found ${clientCount} clients`)

    if (clientCount === 0) {
      console.log('Creating sample clients...')
      await prisma.clients.createMany({
        data: [
          {
            companyname: 'Tech Solutions Inc',
            contactname: '<PERSON>',
            contactemail: '<EMAIL>',
            contactphone: '******-0101',
            address: '123 Tech Street',
            city: 'San Francisco',
            state: 'CA',
            zipcode: '94105',
            country: 'USA'
          },
          {
            companyname: 'Digital Marketing Pro',
            contactname: '<PERSON>',
            contactemail: '<EMAIL>',
            contactphone: '******-0102',
            address: '456 Marketing Ave',
            city: 'New York',
            state: 'NY',
            zipcode: '10001',
            country: 'USA'
          },
          {
            companyname: 'E-commerce Solutions',
            contactname: '<PERSON>',
            contactemail: '<EMAIL>',
            contactphone: '******-0103',
            address: '789 Commerce Blvd',
            city: 'Austin',
            state: 'TX',
            zipcode: '73301',
            country: 'USA'
          }
        ]
      })
    }

    // Get clients for invoice creation
    const clients = await prisma.clients.findMany({ take: 3 })
    console.log(`Using ${clients.length} clients for invoice creation`)

    // Check if we have contracts and orders
    let contractCount = await prisma.contracts.count()
    let orderCount = await prisma.orders.count()

    if (contractCount === 0) {
      console.log('Creating sample contracts...')
      await prisma.contracts.createMany({
        data: clients.map((client, index) => ({
          title: `Service Contract ${index + 1}`,
          description: `Service agreement with ${client.companyname}`,
          clientid: client.id,
          contstartdate: new Date(),
          contenddate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          contvalue: 50000 + (index * 25000),
          contstatus: 'ACTIVE'
        }))
      })
    }

    if (orderCount === 0) {
      console.log('Creating sample orders...')
      await prisma.orders.createMany({
        data: clients.map((client, index) => ({
          ordernumber: `ORD-2024-${String(index + 1).padStart(3, '0')}`,
          description: `Order for ${client.companyname}`,
          clientid: client.id,
          orderdate: new Date(),
          totalamount: 25000 + (index * 10000),
          status: 'CONFIRMED'
        }))
      })
    }

    // Get contracts and orders
    const contracts = await prisma.contracts.findMany({ take: 3 })
    const orders = await prisma.orders.findMany({ take: 3 })

    console.log('Creating sample invoices...')
    
    // Create sample invoices
    const invoiceData = [
      {
        clientid: clients[0].id,
        contid: contracts[0]?.id || clients[0].id,
        orderid: orders[0]?.id || clients[0].id,
        invoicenumber: 'INV-2024-001',
        description: 'Web Development Services - Phase 1',
        subtotal: 15000,
        taxamount: 1200,
        totalamount: 16200,
        status: 'Sent',
        duedate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        createdat: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // 15 days ago
      },
      {
        clientid: clients[1].id,
        contid: contracts[1]?.id || clients[1].id,
        orderid: orders[1]?.id || clients[1].id,
        invoicenumber: 'INV-2024-002',
        description: 'Digital Marketing Campaign',
        subtotal: 8500,
        taxamount: 680,
        totalamount: 9180,
        status: 'Paid',
        duedate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        paidat: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        createdat: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000) // 25 days ago
      },
      {
        clientid: clients[2].id,
        contid: contracts[2]?.id || clients[2].id,
        orderid: orders[2]?.id || clients[2].id,
        invoicenumber: 'INV-2024-003',
        description: 'E-commerce Platform Development',
        subtotal: 22000,
        taxamount: 1760,
        totalamount: 23760,
        status: 'Overdue',
        duedate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago (overdue)
        createdat: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000) // 45 days ago
      },
      {
        clientid: clients[0].id,
        contid: contracts[0]?.id || clients[0].id,
        orderid: orders[0]?.id || clients[0].id,
        invoicenumber: 'INV-2024-004',
        description: 'Web Development Services - Phase 2',
        subtotal: 18000,
        taxamount: 1440,
        totalamount: 19440,
        status: 'Pending',
        duedate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
        createdat: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
      }
    ]

    for (const invoice of invoiceData) {
      const createdInvoice = await prisma.invoices.create({
        data: invoice
      })

      // Create invoice items
      await prisma.invoiceitems.createMany({
        data: [
          {
            invoiceid: createdInvoice.id,
            description: 'Frontend Development',
            quantity: 40,
            unitprice: 150,
            totalprice: 6000
          },
          {
            invoiceid: createdInvoice.id,
            description: 'Backend Development',
            quantity: 60,
            unitprice: 120,
            totalprice: 7200
          },
          {
            invoiceid: createdInvoice.id,
            description: 'UI/UX Design',
            quantity: 20,
            unitprice: 100,
            totalprice: 2000
          }
        ]
      })

      // Create payments for paid invoices
      if (invoice.status === 'Paid') {
        await prisma.payments.create({
          data: {
            invoiceid: createdInvoice.id,
            amount: invoice.totalamount,
            paymentdate: invoice.paidat || new Date(),
            paymentmethod: 'Bank Transfer',
            status: 'Completed',
            notes: 'Payment received via bank transfer'
          }
        })
      }

      console.log(`Created invoice: ${invoice.invoicenumber}`)
    }

    console.log('Invoice seeding completed successfully!')

  } catch (error) {
    console.error('Error seeding invoices:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedInvoices()
