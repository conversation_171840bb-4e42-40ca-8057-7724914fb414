import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON>rror<PERSON>and<PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/clients - List all clients with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}

  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), [
      'companyname',
      'contactname',
      'contactemail',
      'city',
      'state',
      'country'
    ])
    Object.assign(where, searchQuery)
  }

  // Add filters
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      if (filters.isActive !== undefined) where.isactive = filters.isActive === 'true'
    } catch (e) {
      // Invalid filter JSON, ignore
    }
  }

  // Build orderBy clause with field mapping
  const fieldMapping: Record<string, string> = {
    'createdAt': 'createdat',
    'updatedAt': 'updatedat',
    'companyName': 'companyname',
    'contactName': 'contactname',
    'contactEmail': 'contactemail',
    'contactPhone': 'contactphone',
    'contactFax': 'contactfax',
    'companyWebsite': 'companywebsite',
    'zipCode': 'zipcode',
    'logoUrl': 'logourl',
    'isActive': 'isactive'
  }

  const orderBy: any = {}
  if (sortBy) {
    const mappedSortBy = fieldMapping[sortBy] || sortBy
    orderBy[mappedSortBy] = sortOrder || 'asc'
  } else {
    orderBy.updatedat = 'desc' // Default sort by Last Active
  }

  const [clients, total] = await Promise.all([
    prisma.clients.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            projstartdate: true,
            projcompletiondate: true,
            estimatecost: true,
          },
          orderBy: {
            createdat: 'desc',
          },
          take: 3, // Limit to recent projects
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            ordertotalamount: true,
            status: true,
            orderdate: true,
          },
          orderBy: {
            createdat: 'desc',
          },
          take: 3, // Limit to recent orders
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true,
            contvalue: true,
            createdat: true,
          },
          orderBy: {
            createdat: 'desc',
          },
          take: 3, // Limit to recent contracts
        },
        invoices: {
          select: {
            id: true,
            invoicenumber: true,
            totalamount: true,
            status: true,
            createdat: true,
          },
          orderBy: {
            createdat: 'desc',
          },
          take: 3, // Limit to recent invoices
        },
        _count: {
          select: {
            projects: true,
            orders: true,
            invoices: true,
            contracts: true,
            testimonials: true,
          },
        },
      },
    }),
    prisma.clients.count({ where }),
  ])

  // Transform the data for frontend
  const transformedClients = clients.map(client => transformFromDbFields.client(client))

  return paginatedResponse(transformedClients, page, limit, total)
})

// POST /api/admin/clients - Create a new client
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.client.create)
  const data = await validate(request)

  // Check if a client with the same email already exists
  const existingClient = await prisma.clients.findFirst({
    where: {
      contactemail: data.contactEmail,
    },
  })

  if (existingClient) {
    throw new Error('A client with this email already exists')
  }

  // If userId is provided, check if the user exists
  if (data.userId) {
    const user = await prisma.users.findUnique({
      where: { id: Number(data.userId) },
    })

    if (!user) {
      throw new Error('User not found')
    }
  }

  const client = await prisma.clients.create({
    data: transformToDbFields.client(data),
    include: {
      _count: {
        select: {
          projects: true,
          orders: true,
          invoices: true,
          contracts: true,
        },
      },
    },
  })

  const transformedClient = transformFromDbFields.client(client)
  return successResponse(transformedClient, 'Client created successfully', 201)
})

// PUT /api/admin/clients - Bulk update clients
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid client IDs provided' },
      { status: 400 }
    )
  }

  const updatedClients = await prisma.clients.updateMany({
    where: {
      id: {
        in: ids.map(Number),
      },
    },
    data: transformToDbFields.client(data),
  })

  return successResponse(
    { count: updatedClients.count },
    `${updatedClients.count} clients updated successfully`
  )
})

// DELETE /api/admin/clients - Bulk delete clients
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid client IDs provided' },
      { status: 400 }
    )
  }

  // Check if any clients have associated data that should be preserved
  const clientsWithData = await prisma.clients.findMany({
    where: {
      id: { in: ids.map(Number) },
      OR: [
        { projects: { some: {} } },
        { orders: { some: {} } },
        { invoices: { some: {} } },
        { contracts: { some: {} } },
      ],
    },
    select: { id: true, companyname: true },
  })

  if (clientsWithData.length > 0) {
    const clientNames = clientsWithData.map(c => c.companyname).join(', ')
    return NextResponse.json(
      {
        success: false,
        error: `Cannot delete clients with associated data: ${clientNames}. Please handle their projects, orders, invoices, and contracts first.`
      },
      { status: 400 }
    )
  }

  const deletedClients = await prisma.clients.deleteMany({
    where: {
      id: {
        in: ids.map(Number),
      },
    },
  })

  return successResponse(
    { count: deletedClients.count },
    `${deletedClients.count} clients deleted successfully`
  )
})
