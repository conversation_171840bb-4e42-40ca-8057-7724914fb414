import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/invoices - Get all invoices with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['invoicenumber', 'description'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get invoices with pagination
  const [invoices, total] = await Promise.all([
    prisma.invoices.findMany({
      where: searchQuery,
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true
          }
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.invoices.count({ where: searchQuery })
  ])

  // Transform the data to match expected format
  const transformedInvoices = invoices.map(invoice => ({
    id: Number(invoice.id),
    invoiceNumber: invoice.invoicenumber,
    dueDate: invoice.duedate,
    subtotal: Number(invoice.subtotal || 0),
    taxRate: Number(invoice.taxrate || 0),
    taxAmount: Number(invoice.taxamount || 0),
    totalAmount: Number(invoice.totalamount || 0),
    status: invoice.status,
    description: invoice.description,
    clientId: Number(invoice.clientid),
    contractId: invoice.contid ? Number(invoice.contid) : null,
    orderId: invoice.orderid ? Number(invoice.orderid) : null,
    projectId: invoice.projectid ? Number(invoice.projectid) : null,
    paidAt: invoice.paidat,
    createdAt: invoice.createdat,
    updatedAt: invoice.updatedat,
    client: invoice.clients ? {
      id: Number(invoice.clients.id),
      companyName: invoice.clients.companyname,
      contactName: invoice.clients.contactname,
      contactEmail: invoice.clients.contactemail
    } : null,
    project: invoice.projects ? {
      id: Number(invoice.projects.id),
      name: invoice.projects.name,
      status: invoice.projects.status
    } : null,
    order: invoice.orders ? {
      id: Number(invoice.orders.id),
      orderTitle: invoice.orders.ordertitle,
      status: invoice.orders.status
    } : null,
    contract: invoice.contracts ? {
      id: Number(invoice.contracts.id),
      name: invoice.contracts.contname,
      status: invoice.contracts.contstatus
    } : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => ({
      id: Number(item.id),
      description: item.description,
      quantity: Number(item.quantity),
      unitPrice: Number(item.unitprice),
      totalPrice: Number(item.totalprice)
    })) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      id: Number(payment.id),
      amount: Number(payment.amount),
      status: payment.status,
      paymentDate: payment.paymentdate,
      paidAt: payment.paymentdate
    })) : []
  }))

  return paginatedResponse(transformedInvoices, page, limit, total)
})

// POST /api/admin/invoices - Create a new invoice
export const POST = withErrorHandler(async (request: NextRequest) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)

  const body = await request.json()
  console.log('Received invoice data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Validate invoice data
  console.log('Validating invoice data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.create.parse(invoiceData)

  // Transform data for database
  const dbData = transformToDbFields.invoice(validatedData)

  // Create invoice with items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Create the invoice
    const newInvoice = await tx.invoices.create({
      data: dbData,
    })

    // Create invoice items if provided
    if (items && Array.isArray(items) && items.length > 0) {
      const validItems = items.filter(item => item.description && item.description.trim() !== '')

      if (validItems.length > 0) {
        await tx.invoiceitems.createMany({
          data: validItems.map(item => ({
            invoiceid: newInvoice.id,
            description: item.description,
            quantity: Number(item.quantity) || 1,
            unitprice: Number(item.unitPrice) || 0,
            totalprice: Number(item.totalPrice) || 0,
          }))
        })
      }
    }

    // Return the complete invoice with relations
    return await tx.invoices.findUnique({
      where: { id: newInvoice.id },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true
          }
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      }
    })
  })

  // Transform the response
  const transformedInvoice = {
    id: Number(invoice.id),
    invoiceNumber: invoice.invoicenumber,
    dueDate: invoice.duedate,
    subtotal: Number(invoice.subtotal || 0),
    taxRate: Number(invoice.taxrate || 0),
    taxAmount: Number(invoice.taxamount || 0),
    totalAmount: Number(invoice.totalamount || 0),
    status: invoice.status,
    description: invoice.description,
    clientId: Number(invoice.clientid),
    contractId: invoice.contid ? Number(invoice.contid) : null,
    orderId: invoice.orderid ? Number(invoice.orderid) : null,
    projectId: invoice.projectid ? Number(invoice.projectid) : null,
    paidAt: invoice.paidat,
    createdAt: invoice.createdat,
    updatedAt: invoice.updatedat,
    client: invoice.clients ? {
      id: Number(invoice.clients.id),
      companyName: invoice.clients.companyname,
      contactName: invoice.clients.contactname,
      contactEmail: invoice.clients.contactemail
    } : null,
    project: invoice.projects ? {
      id: Number(invoice.projects.id),
      name: invoice.projects.name,
      status: invoice.projects.status
    } : null,
    order: invoice.orders ? {
      id: Number(invoice.orders.id),
      orderTitle: invoice.orders.ordertitle,
      status: invoice.orders.status
    } : null,
    contract: invoice.contracts ? {
      id: Number(invoice.contracts.id),
      name: invoice.contracts.contname,
      status: invoice.contracts.contstatus
    } : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => ({
      id: Number(item.id),
      description: item.description,
      quantity: Number(item.quantity),
      unitPrice: Number(item.unitprice),
      totalPrice: Number(item.totalprice)
    })) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      id: Number(payment.id),
      amount: Number(payment.amount),
      status: payment.status,
      paymentDate: payment.paymentdate,
      paidAt: payment.paymentdate
    })) : []
  }

  return successResponse(transformedInvoice, 'Invoice created successfully', 201)
})
