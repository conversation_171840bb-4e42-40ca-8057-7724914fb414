import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  ApiError
} from '@/lib/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: { slug: string }
}

// GET /api/projects/[slug] - Get a specific public project by slug
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  const { slug } = await params

  const project = await prisma.projects.findFirst({
    where: {
      slug,
      ispublic: true,
      status: 'COMPLETED'
    },
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
        },
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          ordertotalamount: true,
          createdat: true,
        },
      },
      feedbacks: {
        where: {
          ispublic: true,
        },
        select: {
          id: true,
          rating: true,
          comment: true,
          createdat: true,
          clients: {
            select: {
              companyname: true,
              contactname: true,
            },
          },
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      projectdocuments: {
        select: {
          id: true,
          filename: true,
          fileurl: true,
          filetype: true,
          filesize: true,
          createdat: true,
        },
        take: 5, // Limit to 5 documents for performance
      },
      _count: {
        select: {
          feedbacks: {
            where: {
              ispublic: true,
            },
          },
          tasks: true,
          projectdocuments: true,
        },
      },
    },
  })

  if (!project) {
    throw new ApiError('Project not found', 404)
  }

  // Add computed fields
  const enrichedProject = {
    ...project,
    averageRating: project.feedbacks.length > 0 
      ? project.feedbacks.reduce((sum, feedback) => sum + feedback.rating, 0) / project.feedbacks.length
      : null,
    tagsArray: project.tags ? project.tags.split(',').map(tag => tag.trim()) : [],
    duration: project.projstartdate && project.projcompletiondate 
      ? Math.ceil((new Date(project.projcompletiondate).getTime() - new Date(project.projstartdate).getTime()) / (1000 * 60 * 60 * 24))
      : null,
  }

  return successResponse(enrichedProject)
})
