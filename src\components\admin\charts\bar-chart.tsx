'use client'

import { useMemo } from 'react'

interface DataPoint {
  label: string
  value: number
  color?: string
}

interface BarChartProps {
  data: DataPoint[]
  title?: string
  height?: number
  color?: string
  showGrid?: boolean
  showValues?: boolean
  formatValue?: (value: number) => string
  className?: string
  horizontal?: boolean
}

export default function BarChart({
  data,
  title,
  height = 200,
  color = '#3B82F6',
  showGrid = true,
  showValues = true,
  formatValue = (value) => value.toString(),
  className = '',
  horizontal = false
}: BarChartProps) {
  const { bars, maxValue } = useMemo(() => {
    if (!data || data.length === 0) {
      return { bars: [], maxValue: 0 }
    }

    const values = data.map(d => d.value)
    const maxVal = Math.max(...values) || 1

    const width = 400
    const chartHeight = height - 60 // Leave space for labels
    const barWidth = horizontal ? chartHeight / data.length : width / data.length
    const barSpacing = barWidth * 0.2
    const actualBarWidth = barWidth - barSpacing

    const chartBars = data.map((point, index) => {
      if (horizontal) {
        const barHeight = actualBarWidth
        const barLength = (point.value / maxVal) * (width - 60)
        const y = index * barWidth + barSpacing / 2
        const x = 0
        
        return {
          x,
          y,
          width: barLength,
          height: barHeight,
          value: point.value,
          label: point.label,
          color: point.color || color
        }
      } else {
        const barHeight = (point.value / maxVal) * chartHeight
        const x = index * barWidth + barSpacing / 2
        const y = chartHeight - barHeight
        
        return {
          x,
          y,
          width: actualBarWidth,
          height: barHeight,
          value: point.value,
          label: point.label,
          color: point.color || color
        }
      }
    })

    return { bars: chartBars, maxValue: maxVal }
  }, [data, height, color, horizontal])

  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
        <div className="flex items-center justify-center h-48 text-gray-500">
          No data available
        </div>
      </div>
    )
  }

  const svgHeight = horizontal ? height : height + 40
  const svgWidth = horizontal ? 500 : 400

  return (
    <div className={`bg-white rounded-lg border p-4 ${className}`}>
      {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
      
      <div className="relative">
        <svg width="100%" height={svgHeight} viewBox={`0 0 ${svgWidth} ${svgHeight}`} className="overflow-visible">
          {/* Grid lines */}
          {showGrid && (
            <g className="opacity-20">
              {horizontal ? (
                // Vertical grid lines for horizontal bars
                [0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
                  const x = (svgWidth - 100) * ratio
                  return (
                    <line
                      key={`grid-${index}`}
                      x1={x}
                      y1="0"
                      x2={x}
                      y2={height - 60}
                      stroke="#6B7280"
                      strokeWidth="1"
                    />
                  )
                })
              ) : (
                // Horizontal grid lines for vertical bars
                [0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
                  const y = (height - 60) * ratio
                  return (
                    <line
                      key={`grid-${index}`}
                      x1="0"
                      y1={y}
                      x2="400"
                      y2={y}
                      stroke="#6B7280"
                      strokeWidth="1"
                    />
                  )
                })
              )}
            </g>
          )}

          {/* Bars */}
          {bars.map((bar, index) => (
            <g key={index}>
              <rect
                x={bar.x}
                y={bar.y}
                width={bar.width}
                height={bar.height}
                fill={bar.color}
                className="hover:opacity-80 transition-opacity cursor-pointer"
                rx="2"
              />
              
              {/* Value labels */}
              {showValues && (
                <text
                  x={horizontal ? bar.x + bar.width + 5 : bar.x + bar.width / 2}
                  y={horizontal ? bar.y + bar.height / 2 + 4 : bar.y - 5}
                  textAnchor={horizontal ? "start" : "middle"}
                  fontSize="12"
                  fill="#374151"
                  fontWeight="500"
                >
                  {formatValue(bar.value)}
                </text>
              )}
              
              {/* Tooltip on hover */}
              <g className="opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
                <rect
                  x={horizontal ? bar.x + bar.width / 2 - 40 : bar.x + bar.width / 2 - 40}
                  y={horizontal ? bar.y - 35 : bar.y + bar.height / 2 - 15}
                  width="80"
                  height="30"
                  fill="black"
                  fillOpacity="0.8"
                  rx="4"
                />
                <text
                  x={horizontal ? bar.x + bar.width / 2 : bar.x + bar.width / 2}
                  y={horizontal ? bar.y - 20 : bar.y + bar.height / 2 - 5}
                  textAnchor="middle"
                  fill="white"
                  fontSize="11"
                  fontWeight="500"
                >
                  {bar.label}
                </text>
                <text
                  x={horizontal ? bar.x + bar.width / 2 : bar.x + bar.width / 2}
                  y={horizontal ? bar.y - 8 : bar.y + bar.height / 2 + 8}
                  textAnchor="middle"
                  fill="white"
                  fontSize="11"
                  fontWeight="500"
                >
                  {formatValue(bar.value)}
                </text>
              </g>
            </g>
          ))}

          {/* Axis labels */}
          <g className="text-xs fill-gray-500">
            {horizontal ? (
              // Y-axis labels for horizontal bars
              data.map((point, index) => {
                const y = bars[index].y + bars[index].height / 2 + 4
                return (
                  <text
                    key={`label-${index}`}
                    x="-5"
                    y={y}
                    textAnchor="end"
                    fontSize="10"
                    fill="#6B7280"
                  >
                    {point.label}
                  </text>
                )
              })
            ) : (
              // X-axis labels for vertical bars
              data.map((point, index) => {
                const x = bars[index].x + bars[index].width / 2
                return (
                  <text
                    key={`label-${index}`}
                    x={x}
                    y={height - 20}
                    textAnchor="middle"
                    fontSize="10"
                    fill="#6B7280"
                    transform={`rotate(-45, ${x}, ${height - 20})`}
                  >
                    {point.label}
                  </text>
                )
              })
            )}
          </g>

          {/* Value axis labels */}
          <g className="text-xs fill-gray-500">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
              const value = maxValue * ratio
              
              if (horizontal) {
                const x = (svgWidth - 100) * ratio
                return (
                  <text
                    key={`value-${index}`}
                    x={x}
                    y={height - 45}
                    textAnchor="middle"
                    fontSize="10"
                    fill="#6B7280"
                  >
                    {formatValue(value)}
                  </text>
                )
              } else {
                const y = (height - 60) * (1 - ratio)
                return (
                  <text
                    key={`value-${index}`}
                    x="-5"
                    y={y + 4}
                    textAnchor="end"
                    fontSize="10"
                    fill="#6B7280"
                  >
                    {formatValue(value)}
                  </text>
                )
              }
            })}
          </g>
        </svg>
      </div>
    </div>
  )
}
