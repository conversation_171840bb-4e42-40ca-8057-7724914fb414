'use client'

import { useMemo } from 'react'

interface DataPoint {
  label: string
  value: number
  color?: string
}

interface DonutChartProps {
  data: DataPoint[]
  title?: string
  size?: number
  innerRadius?: number
  showLegend?: boolean
  showValues?: boolean
  formatValue?: (value: number) => string
  className?: string
  colors?: string[]
}

const DEFAULT_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
]

export default function DonutChart({
  data,
  title,
  size = 200,
  innerRadius = 0.6,
  showLegend = true,
  showValues = true,
  formatValue = (value) => value.toString(),
  className = '',
  colors = DEFAULT_COLORS
}: DonutChartProps) {
  const { segments, total, center } = useMemo(() => {
    if (!data || data.length === 0) {
      return { segments: [], total: 0, center: size / 2 }
    }

    const totalValue = data.reduce((sum, item) => sum + item.value, 0)
    const centerPoint = size / 2
    const outerRadius = centerPoint - 10
    const innerRad = outerRadius * innerRadius

    let currentAngle = -90 // Start from top

    const chartSegments = data.map((item, index) => {
      const percentage = (item.value / totalValue) * 100
      const angle = (item.value / totalValue) * 360
      const startAngle = currentAngle
      const endAngle = currentAngle + angle

      // Calculate path for the arc
      const startAngleRad = (startAngle * Math.PI) / 180
      const endAngleRad = (endAngle * Math.PI) / 180

      const x1 = centerPoint + outerRadius * Math.cos(startAngleRad)
      const y1 = centerPoint + outerRadius * Math.sin(startAngleRad)
      const x2 = centerPoint + outerRadius * Math.cos(endAngleRad)
      const y2 = centerPoint + outerRadius * Math.sin(endAngleRad)

      const x3 = centerPoint + innerRad * Math.cos(endAngleRad)
      const y3 = centerPoint + innerRad * Math.sin(endAngleRad)
      const x4 = centerPoint + innerRad * Math.cos(startAngleRad)
      const y4 = centerPoint + innerRad * Math.sin(startAngleRad)

      const largeArcFlag = angle > 180 ? 1 : 0

      const pathData = [
        `M ${x1} ${y1}`,
        `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        `L ${x3} ${y3}`,
        `A ${innerRad} ${innerRad} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
        'Z'
      ].join(' ')

      // Calculate label position
      const labelAngle = (startAngle + endAngle) / 2
      const labelAngleRad = (labelAngle * Math.PI) / 180
      const labelRadius = (outerRadius + innerRad) / 2
      const labelX = centerPoint + labelRadius * Math.cos(labelAngleRad)
      const labelY = centerPoint + labelRadius * Math.sin(labelAngleRad)

      currentAngle = endAngle

      return {
        path: pathData,
        value: item.value,
        label: item.label,
        percentage,
        color: item.color || colors[index % colors.length],
        labelX,
        labelY,
        angle: labelAngle
      }
    })

    return {
      segments: chartSegments,
      total: totalValue,
      center: centerPoint
    }
  }, [data, size, innerRadius, colors])

  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
        <div className="flex items-center justify-center h-48 text-gray-500">
          No data available
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border p-4 ${className}`}>
      {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
      
      <div className="flex items-center justify-center space-x-8">
        {/* Chart */}
        <div className="relative">
          <svg width={size} height={size} className="overflow-visible">
            {/* Segments */}
            {segments.map((segment, index) => (
              <g key={index}>
                <path
                  d={segment.path}
                  fill={segment.color}
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                  stroke="white"
                  strokeWidth="2"
                />
                
                {/* Value labels on segments */}
                {showValues && segment.percentage > 5 && (
                  <text
                    x={segment.labelX}
                    y={segment.labelY}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize="12"
                    fill="white"
                    fontWeight="600"
                    className="pointer-events-none"
                  >
                    {segment.percentage.toFixed(0)}%
                  </text>
                )}
                
                {/* Tooltip on hover */}
                <g className="opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
                  <rect
                    x={segment.labelX - 40}
                    y={segment.labelY - 25}
                    width="80"
                    height="35"
                    fill="black"
                    fillOpacity="0.8"
                    rx="4"
                  />
                  <text
                    x={segment.labelX}
                    y={segment.labelY - 8}
                    textAnchor="middle"
                    fill="white"
                    fontSize="11"
                    fontWeight="500"
                  >
                    {segment.label}
                  </text>
                  <text
                    x={segment.labelX}
                    y={segment.labelY + 6}
                    textAnchor="middle"
                    fill="white"
                    fontSize="11"
                    fontWeight="500"
                  >
                    {formatValue(segment.value)}
                  </text>
                </g>
              </g>
            ))}
            
            {/* Center text */}
            <text
              x={center}
              y={center - 8}
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="14"
              fill="#6B7280"
              fontWeight="500"
            >
              Total
            </text>
            <text
              x={center}
              y={center + 8}
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="18"
              fill="#111827"
              fontWeight="700"
            >
              {formatValue(total)}
            </text>
          </svg>
        </div>

        {/* Legend */}
        {showLegend && (
          <div className="space-y-2">
            {segments.map((segment, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: segment.color }}
                />
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{segment.label}</div>
                  <div className="text-gray-500">
                    {formatValue(segment.value)} ({segment.percentage.toFixed(1)}%)
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
