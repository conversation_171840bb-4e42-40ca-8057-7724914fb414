'use client'

import { useMemo } from 'react'

interface DataPoint {
  label: string
  value: number
  date?: Date
}

interface LineChartProps {
  data: DataPoint[]
  title?: string
  height?: number
  color?: string
  showGrid?: boolean
  showDots?: boolean
  formatValue?: (value: number) => string
  className?: string
}

export default function LineChart({
  data,
  title,
  height = 200,
  color = '#3B82F6',
  showGrid = true,
  showDots = true,
  formatValue = (value) => value.toString(),
  className = ''
}: LineChartProps) {
  const { pathData, points, maxValue, minValue } = useMemo(() => {
    if (!data || data.length === 0) {
      return { pathData: '', points: [], maxValue: 0, minValue: 0 }
    }

    const values = data.map(d => d.value)
    const maxVal = Math.max(...values)
    const minVal = Math.min(...values)
    const range = maxVal - minVal || 1

    const width = 400
    const chartHeight = height - 40 // Leave space for labels
    const stepX = width / (data.length - 1 || 1)

    const chartPoints = data.map((point, index) => {
      const x = index * stepX
      const y = chartHeight - ((point.value - minVal) / range) * chartHeight
      return { x, y, value: point.value, label: point.label }
    })

    const pathData = chartPoints.reduce((path, point, index) => {
      const command = index === 0 ? 'M' : 'L'
      return `${path} ${command} ${point.x} ${point.y}`
    }, '')

    return {
      pathData,
      points: chartPoints,
      maxValue: maxVal,
      minValue: minVal
    }
  }, [data, height])

  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
        <div className="flex items-center justify-center h-48 text-gray-500">
          No data available
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border p-4 ${className}`}>
      {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
      
      <div className="relative">
        <svg width="100%" height={height} viewBox={`0 0 400 ${height}`} className="overflow-visible">
          {/* Grid lines */}
          {showGrid && (
            <g className="opacity-20">
              {/* Horizontal grid lines */}
              {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
                const y = (height - 40) * ratio
                return (
                  <line
                    key={`h-grid-${index}`}
                    x1="0"
                    y1={y}
                    x2="400"
                    y2={y}
                    stroke="#6B7280"
                    strokeWidth="1"
                  />
                )
              })}
              
              {/* Vertical grid lines */}
              {data.map((_, index) => {
                const x = (400 / (data.length - 1 || 1)) * index
                return (
                  <line
                    key={`v-grid-${index}`}
                    x1={x}
                    y1="0"
                    x2={x}
                    y2={height - 40}
                    stroke="#6B7280"
                    strokeWidth="1"
                  />
                )
              })}
            </g>
          )}

          {/* Area under the line */}
          <defs>
            <linearGradient id={`gradient-${title}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={color} stopOpacity="0.2" />
              <stop offset="100%" stopColor={color} stopOpacity="0.05" />
            </linearGradient>
          </defs>
          
          {pathData && (
            <path
              d={`${pathData} L ${points[points.length - 1]?.x || 0} ${height - 40} L 0 ${height - 40} Z`}
              fill={`url(#gradient-${title})`}
            />
          )}

          {/* Main line */}
          {pathData && (
            <path
              d={pathData}
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}

          {/* Data points */}
          {showDots && points.map((point, index) => (
            <g key={index}>
              <circle
                cx={point.x}
                cy={point.y}
                r="4"
                fill="white"
                stroke={color}
                strokeWidth="2"
                className="hover:r-6 transition-all cursor-pointer"
              />
              
              {/* Tooltip on hover */}
              <g className="opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
                <rect
                  x={point.x - 30}
                  y={point.y - 35}
                  width="60"
                  height="25"
                  fill="black"
                  fillOpacity="0.8"
                  rx="4"
                />
                <text
                  x={point.x}
                  y={point.y - 18}
                  textAnchor="middle"
                  fill="white"
                  fontSize="12"
                  fontWeight="500"
                >
                  {formatValue(point.value)}
                </text>
              </g>
            </g>
          ))}

          {/* Y-axis labels */}
          <g className="text-xs fill-gray-500">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
              const y = (height - 40) * ratio
              const value = minValue + (maxValue - minValue) * (1 - ratio)
              return (
                <text
                  key={`y-label-${index}`}
                  x="-5"
                  y={y + 4}
                  textAnchor="end"
                  fontSize="10"
                  fill="#6B7280"
                >
                  {formatValue(value)}
                </text>
              )
            })}
          </g>

          {/* X-axis labels */}
          <g className="text-xs fill-gray-500">
            {data.map((point, index) => {
              const x = (400 / (data.length - 1 || 1)) * index
              return (
                <text
                  key={`x-label-${index}`}
                  x={x}
                  y={height - 10}
                  textAnchor="middle"
                  fontSize="10"
                  fill="#6B7280"
                >
                  {point.label}
                </text>
              )
            })}
          </g>
        </svg>
      </div>
    </div>
  )
}
