'use client'

import { useState, useEffect } from 'react'
import { 
  BuildingOfficeIcon, 
  FolderIcon, 
  DocumentTextIcon, 
  CurrencyDollarIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import ProjectForm from '../projects/project-form'
import ContractForm from '../contracts/contract-form'
import InvoiceForm from '../invoices/invoice-form'

interface Client {
  id: number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  isActive: boolean
  createdAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
  }
}

interface Project {
  id: number
  name: string
  status: string
  estimatedCost: number
  startDate?: string
  completionDate?: string
  createdAt: string
}

interface Contract {
  id: number
  name: string
  status: string
  value: number
  serviceType?: string
  createdAt: string
}

interface Invoice {
  id: number
  invoiceNumber: string
  totalAmount: number
  status: string
  dueDate?: string
  paidAt?: string
  createdAt: string
}

interface ClientDashboardProps {
  clientId: number
}

export default function ClientDashboard({ clientId }: ClientDashboardProps) {
  const [client, setClient] = useState<Client | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [contracts, setContracts] = useState<Contract[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  // Form modal states
  const [projectFormOpen, setProjectFormOpen] = useState(false)
  const [contractFormOpen, setContractFormOpen] = useState(false)
  const [invoiceFormOpen, setInvoiceFormOpen] = useState(false)

  useEffect(() => {
    if (clientId) {
      fetchClientData()
    }
  }, [clientId])

  const fetchClientData = async () => {
    setLoading(true)
    try {
      // Fetch client details
      const clientResponse = await fetch(`/api/admin/clients/${clientId}`)
      if (clientResponse.ok) {
        const clientData = await clientResponse.json()
        setClient(clientData.data)
      }

      // Fetch related data
      await Promise.all([
        fetchProjects(),
        fetchContracts(),
        fetchInvoices()
      ])
    } catch (error) {
      console.error('Error fetching client data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchProjects = async () => {
    try {
      const response = await fetch(`/api/admin/projects?clientId=${clientId}&limit=10`)
      if (response.ok) {
        const data = await response.json()
        setProjects(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    }
  }

  const fetchContracts = async () => {
    try {
      const response = await fetch(`/api/admin/contracts?clientId=${clientId}&limit=10`)
      if (response.ok) {
        const data = await response.json()
        setContracts(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching contracts:', error)
    }
  }

  const fetchInvoices = async () => {
    try {
      const response = await fetch(`/api/admin/invoices?clientId=${clientId}&limit=10`)
      if (response.ok) {
        const data = await response.json()
        setInvoices(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching invoices:', error)
    }
  }

  // Handler functions for creating new items
  const handleCreateProject = async (projectData: any) => {
    try {
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...projectData,
          clientId: clientId
        })
      })

      if (response.ok) {
        setProjectFormOpen(false)
        await fetchProjects()
        await fetchClientData() // Refresh counts
      } else {
        throw new Error('Failed to create project')
      }
    } catch (error) {
      console.error('Error creating project:', error)
      alert('Failed to create project')
    }
  }

  const handleCreateContract = async (contractData: any) => {
    try {
      const response = await fetch('/api/admin/contracts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...contractData,
          clientId: clientId
        })
      })

      if (response.ok) {
        setContractFormOpen(false)
        await fetchContracts()
        await fetchClientData() // Refresh counts
      } else {
        throw new Error('Failed to create contract')
      }
    } catch (error) {
      console.error('Error creating contract:', error)
      alert('Failed to create contract')
    }
  }

  const handleCreateInvoice = async (invoiceData: any) => {
    try {
      const response = await fetch('/api/admin/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...invoiceData,
          clientId: clientId
        })
      })

      if (response.ok) {
        setInvoiceFormOpen(false)
        await fetchInvoices()
        await fetchClientData() // Refresh counts
      } else {
        throw new Error('Failed to create invoice')
      }
    } catch (error) {
      console.error('Error creating invoice:', error)
      alert('Failed to create invoice')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'inactive':
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'pending':
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'in_progress':
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Client not found</p>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'projects', name: 'Projects', icon: FolderIcon, count: client._count.projects },
    { id: 'contracts', name: 'Contracts', icon: DocumentTextIcon, count: client._count.contracts },
    { id: 'invoices', name: 'Invoices', icon: CurrencyDollarIcon, count: client._count.invoices }
  ]

  return (
    <div className="space-y-6">
      {/* Client Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center">
              <BuildingOfficeIcon className="h-8 w-8 text-gray-500" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{client.companyName}</h1>
              <p className="text-gray-600">{client.contactName}</p>
              <p className="text-sm text-gray-500">{client.contactEmail}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {client.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FolderIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Projects</dt>
                  <dd className="text-lg font-medium text-gray-900">{client._count.projects}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Contracts</dt>
                  <dd className="text-lg font-medium text-gray-900">{client._count.contracts}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Invoices</dt>
                  <dd className="text-lg font-medium text-gray-900">{client._count.invoices}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BuildingOfficeIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Orders</dt>
                  <dd className="text-lg font-medium text-gray-900">{client._count.orders}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                  {tab.count !== undefined && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      activeTab === tab.id ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Client Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Company:</span>
                    <span className="ml-2 text-sm text-gray-900">{client.companyName}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Contact:</span>
                    <span className="ml-2 text-sm text-gray-900">{client.contactName}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Email:</span>
                    <span className="ml-2 text-sm text-gray-900">{client.contactEmail}</span>
                  </div>
                  {client.contactPhone && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Phone:</span>
                      <span className="ml-2 text-sm text-gray-900">{client.contactPhone}</span>
                    </div>
                  )}
                  {client.website && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Website:</span>
                      <span className="ml-2 text-sm text-gray-900">{client.website}</span>
                    </div>
                  )}
                  {(client.address || client.city || client.state || client.country) && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Address:</span>
                      <span className="ml-2 text-sm text-gray-900">
                        {[client.address, client.city, client.state, client.country]
                          .filter(Boolean)
                          .join(', ')}
                      </span>
                    </div>
                  )}
                  <div>
                    <span className="text-sm font-medium text-gray-500">Member Since:</span>
                    <span className="ml-2 text-sm text-gray-900">{formatDate(client.createdAt)}</span>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Projects:</span>
                    <span className="text-sm text-gray-900">{client._count.projects}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Active Contracts:</span>
                    <span className="text-sm text-gray-900">{client._count.contracts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Invoices:</span>
                    <span className="text-sm text-gray-900">{client._count.invoices}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Total Orders:</span>
                    <span className="text-sm text-gray-900">{client._count.orders}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'projects' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Projects</h3>
                <button
                  onClick={() => setProjectFormOpen(true)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Project
                </button>
              </div>
              {projects.length > 0 ? (
                <div className="space-y-4">
                  {projects.map((project) => (
                    <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{project.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-500">Estimated Cost:</span> {formatCurrency(project.estimatedCost)}
                        </div>
                        <div>
                          <span className="font-medium text-gray-500">Created:</span> {formatDate(project.createdAt)}
                        </div>
                        {project.startDate && (
                          <div>
                            <span className="font-medium text-gray-500">Start Date:</span> {formatDate(project.startDate)}
                          </div>
                        )}
                        {project.completionDate && (
                          <div>
                            <span className="font-medium text-gray-500">Completion:</span> {formatDate(project.completionDate)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating a new project.</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'contracts' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Contracts</h3>
                <button
                  onClick={() => setContractFormOpen(true)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Contract
                </button>
              </div>
              {contracts.length > 0 ? (
                <div className="space-y-4">
                  {contracts.map((contract) => (
                    <div key={contract.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{contract.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(contract.status)}`}>
                          {contract.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-500">Value:</span> {formatCurrency(contract.value)}
                        </div>
                        <div>
                          <span className="font-medium text-gray-500">Created:</span> {formatDate(contract.createdAt)}
                        </div>
                        {contract.serviceType && (
                          <div>
                            <span className="font-medium text-gray-500">Service Type:</span> {contract.serviceType}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No contracts</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating a new contract.</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'invoices' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Invoices</h3>
                <button
                  onClick={() => setInvoiceFormOpen(true)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Invoice
                </button>
              </div>
              {invoices.length > 0 ? (
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{invoice.invoiceNumber}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-500">Amount:</span> {formatCurrency(invoice.totalAmount)}
                        </div>
                        <div>
                          <span className="font-medium text-gray-500">Created:</span> {formatDate(invoice.createdAt)}
                        </div>
                        {invoice.dueDate && (
                          <div>
                            <span className="font-medium text-gray-500">Due Date:</span> {formatDate(invoice.dueDate)}
                          </div>
                        )}
                        {invoice.paidAt && (
                          <div>
                            <span className="font-medium text-gray-500">Paid At:</span> {formatDate(invoice.paidAt)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating a new invoice.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Form Modals */}
      <ProjectForm
        isOpen={projectFormOpen}
        onClose={() => setProjectFormOpen(false)}
        onSubmit={handleCreateProject}
        title="Create New Project"
        preSelectedClientId={clientId}
        mode="create"
      />

      <ContractForm
        isOpen={contractFormOpen}
        onClose={() => setContractFormOpen(false)}
        onSubmit={handleCreateContract}
        title="Create New Contract"
        preSelectedClientId={clientId}
        mode="create"
      />

      <InvoiceForm
        isOpen={invoiceFormOpen}
        onClose={() => setInvoiceFormOpen(false)}
        onSubmit={handleCreateInvoice}
        title="Create New Invoice"
        preSelectedClientId={clientId}
        mode="create"
      />
    </div>
  )
}
