'use client'

import React from 'react'
import ClientCard from './client-card'
import { BuildingOfficeIcon } from '@heroicons/react/24/outline'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  isActive: boolean
  createdAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
    testimonials: number
  }
  recentProjects?: Array<{
    id: string
    name: string
    status: string
    createdAt: string
  }>
  recentContracts?: Array<{
    id: string
    name: string
    status: string
    value: number
    createdAt: string
  }>
  recentInvoices?: Array<{
    id: string
    invoiceNumber: string
    totalAmount: number
    status: string
    createdAt: string
  }>
  recentOrders?: Array<{
    id: string
    title: string
    totalAmount: number
    status: string
    createdAt: string
  }>
}

interface ClientGridProps {
  clients: Client[]
  loading?: boolean
  onView: (client: Client) => void
  onEdit: (client: Client) => void
  onDelete: (id: string) => void
  onViewProjects: (client: Client) => void
  onViewContracts: (client: Client) => void
  onViewInvoices: (client: Client) => void
  onViewPayments: (client: Client) => void
}

export default function ClientGrid({
  clients,
  loading = false,
  onView,
  onEdit,
  onDelete,
  onViewProjects,
  onViewContracts,
  onViewInvoices,
  onViewPayments
}: ClientGridProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
              <div className="space-y-2 mb-4">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                <div className="h-3 bg-gray-200 rounded w-4/6"></div>
              </div>
              <div className="grid grid-cols-4 gap-2">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (clients.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <BuildingOfficeIcon className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
        <p className="text-gray-500">Get started by adding your first client.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {clients.map((client) => (
        <ClientCard
          key={client.id}
          client={client}
          onView={onView}
          onEdit={onEdit}
          onDelete={onDelete}
          onViewProjects={onViewProjects}
          onViewContracts={onViewContracts}
          onViewInvoices={onViewInvoices}
          onViewPayments={onViewPayments}
        />
      ))}
    </div>
  )
}
