'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ViewColumnsIcon,
  Squares2X2Icon,
  PlusIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

import ClientTable from './client-table'
import ClientGrid from './client-grid'
import ClientSummaryCards from './client-summary-cards'
import ClientProfileModal from './client-profile-modal'
import ContractModal from './contract-modal'
import ClientFormModal from './client-form-modal'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  isActive: boolean
  createdAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
    testimonials: number
  }
  recentProjects?: Array<{
    id: string
    name: string
    status: string
    createdAt: string
  }>
  recentContracts?: Array<{
    id: string
    name: string
    status: string
    value: number
    createdAt: string
  }>
  recentInvoices?: Array<{
    id: string
    invoiceNumber: string
    totalAmount: number
    status: string
    createdAt: string
  }>
  recentOrders?: Array<{
    id: string
    title: string
    totalAmount: number
    status: string
    createdAt: string
  }>
}

interface ClientStats {
  totalClients: number
  activeClients: number
  inactiveClients: number
  newClientsThisMonth: number
  totalProjects: number
  activeProjects: number
  totalContracts: number
  activeContracts: number
  totalContractValue: number
  totalInvoices: number
  paidInvoices: number
  pendingInvoices: number
  overdueInvoices: number
  totalRevenue: number
  monthlyGrowth: number
  averageProjectValue: number
  clientRetentionRate: number
}

export default function ClientManager() {
  const [clients, setClients] = useState<Client[]>([])
  const [stats, setStats] = useState<ClientStats>({
    totalClients: 0,
    activeClients: 0,
    inactiveClients: 0,
    newClientsThisMonth: 0,
    totalProjects: 0,
    activeProjects: 0,
    totalContracts: 0,
    activeContracts: 0,
    totalContractValue: 0,
    totalInvoices: 0,
    paidInvoices: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
    totalRevenue: 0,
    monthlyGrowth: 0,
    averageProjectValue: 0,
    clientRetentionRate: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')

  // Modal states
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [profileModalOpen, setProfileModalOpen] = useState(false)
  const [profileModalInitialTab, setProfileModalInitialTab] = useState('overview')
  const [contractModalOpen, setContractModalOpen] = useState(false)
  const [contractModalMode, setContractModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [selectedContract, setSelectedContract] = useState(null)
  const [clientFormModalOpen, setClientFormModalOpen] = useState(false)
  const [clientFormMode, setClientFormMode] = useState<'create' | 'edit'>('create')

  const fetchClients = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        search: searchTerm,
        sortBy,
        sortOrder,
        ...(statusFilter !== 'all' && { filter: statusFilter })
      })

      // Fetch clients and analytics in parallel
      const [clientsResponse, analyticsResponse] = await Promise.all([
        fetch(`/api/admin/clients?${params}`),
        fetch('/api/admin/clients/analytics')
      ])

      if (!clientsResponse.ok) {
        const errorText = await clientsResponse.text()
        console.error('API Error Response:', clientsResponse.status, clientsResponse.statusText, errorText)
        throw new Error(`Failed to fetch clients: ${clientsResponse.status} ${clientsResponse.statusText}`)
      }

      const [clientsResult, analyticsResult] = await Promise.all([
        clientsResponse.json(),
        analyticsResponse.json()
      ])

      if (clientsResult.success) {
        setClients(clientsResult.data || [])
        setTotalPages(Math.ceil((clientsResult.total || 0) / 12))
      }

      if (analyticsResult.success) {
        const analytics = analyticsResult.data
        const newStats: ClientStats = {
          totalClients: analytics.totalClients,
          activeClients: analytics.activeClients,
          inactiveClients: analytics.inactiveClients,
          newClientsThisMonth: analytics.newClientsThisMonth,
          totalProjects: analytics.totalProjects,
          activeProjects: analytics.activeProjects,
          totalContracts: analytics.totalContracts,
          activeContracts: analytics.activeContracts,
          totalContractValue: analytics.totalContractValue,
          totalInvoices: analytics.totalInvoices,
          paidInvoices: analytics.paidInvoices,
          pendingInvoices: analytics.pendingInvoices,
          overdueInvoices: analytics.overdueInvoices,
          totalRevenue: analytics.totalRevenue,
          monthlyGrowth: analytics.clientGrowthRate,
          averageProjectValue: analytics.averageProjectValue,
          clientRetentionRate: analytics.clientRetentionRate
        }
        setStats(newStats)
      }

      if (!clientsResult.success) {
        console.error('API returned unsuccessful response:', clientsResult)
        throw new Error(clientsResult.message || 'Failed to fetch clients')
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
      // Set empty data on error to prevent UI crashes
      setClients([])
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchTerm, statusFilter, sortBy, sortOrder])

  useEffect(() => {
    fetchClients()
  }, [fetchClients])

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1)
  }

  const handleFilterChange = (filter: string) => {
    setStatusFilter(filter)
    setCurrentPage(1)
  }

  const handleSort = (field: string, order: 'asc' | 'desc') => {
    setSortBy(field)
    setSortOrder(order)
    setCurrentPage(1)
  }

  const handleViewClient = (client: Client) => {
    setSelectedClient(client)
    setProfileModalInitialTab('overview')
    setProfileModalOpen(true)
  }

  const handleEditClient = (client: Client) => {
    setSelectedClient(client)
    setClientFormMode('edit')
    setClientFormModalOpen(true)
  }

  const handleAddClient = () => {
    setSelectedClient(null)
    setClientFormMode('create')
    setClientFormModalOpen(true)
  }

  const handleDeleteClient = async (clientId: string) => {
    if (!confirm('Are you sure you want to delete this client?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/clients/${clientId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchClients()
      } else {
        console.error('Failed to delete client')
      }
    } catch (error) {
      console.error('Error deleting client:', error)
    }
  }

  const handleViewProjects = (client: Client) => {
    setSelectedClient(client)
    setProfileModalInitialTab('projects')
    setProfileModalOpen(true)
  }

  const handleViewContracts = (client: Client) => {
    setSelectedClient(client)
    setProfileModalInitialTab('contracts')
    setProfileModalOpen(true)
  }

  const handleViewInvoices = (client: Client) => {
    setSelectedClient(client)
    setProfileModalInitialTab('invoices')
    setProfileModalOpen(true)
  }

  const handleViewPayments = (client: Client) => {
    setSelectedClient(client)
    setProfileModalInitialTab('invoices')
    setProfileModalOpen(true)
  }

  const handleSaveContract = async (contractData: any) => {
    try {
      const response = await fetch('/api/admin/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contractData)
      })

      if (response.ok) {
        setContractModalOpen(false)
        await fetchClients() // Refresh data
      } else {
        console.error('Failed to save contract')
      }
    } catch (error) {
      console.error('Error saving contract:', error)
    }
  }

  const handleSaveClient = async (clientData: any) => {
    try {
      // Map frontend field names to database field names
      const mappedData = {
        companyname: clientData.companyName,
        contactname: clientData.contactName,
        contactposition: clientData.contactPosition,
        contactemail: clientData.contactEmail,
        contactphone: clientData.contactPhone,
        contactfax: clientData.contactFax,
        companywebsite: clientData.website,
        address: clientData.address,
        city: clientData.city,
        state: clientData.state,
        zipcode: clientData.zipCode,
        country: clientData.country,
        logourl: clientData.logoUrl,
        notes: clientData.notes,
        isactive: clientData.isActive
      }

      const url = clientFormMode === 'create' ? '/api/admin/clients' : `/api/admin/clients/${clientData.id}`
      const method = clientFormMode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(mappedData)
      })

      if (response.ok) {
        setClientFormModalOpen(false)
        await fetchClients() // Refresh data
      } else {
        const errorData = await response.json()
        console.error('Failed to save client:', errorData)
        alert('Failed to save client: ' + (errorData.message || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error saving client:', error)
      alert('Error saving client: ' + error.message)
    }
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Summary Cards */}
      <ClientSummaryCards stats={stats} loading={loading} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Clients</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage your clients and their associated projects, contracts, and invoices.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => {/* TODO: Implement export */}}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={handleAddClient}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Client
          </button>
        </div>
      </div>

      {/* Enhanced Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
          {/* Search */}
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="Search clients by company, contact, email..."
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <FunnelIcon className="h-5 w-5 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => handleFilterChange(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>

            {/* Sort Options */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Sort by:</span>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-')
                  handleSort(field, order as 'asc' | 'desc')
                }}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="companyName-asc">Company A-Z</option>
                <option value="companyName-desc">Company Z-A</option>
                <option value="contactName-asc">Contact A-Z</option>
                <option value="contactName-desc">Contact Z-A</option>
              </select>
            </div>

            {/* View Toggle */}
            <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  viewMode === 'table'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ViewColumnsIcon className="h-4 w-4 mr-1 inline" />
                Table
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Squares2X2Icon className="h-4 w-4 mr-1 inline" />
                Grid
              </button>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
          <div>
            Showing {clients.length} of {stats.totalClients} clients
            {searchTerm && ` matching "${searchTerm}"`}
            {statusFilter !== 'all' && ` (${statusFilter} only)`}
          </div>
          <div className="flex items-center space-x-4">
            <span>{stats.activeClients} active</span>
            <span>{stats.inactiveClients} inactive</span>
          </div>
        </div>
      </div>

      {/* Client List */}
      {viewMode === 'table' ? (
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <ClientTable
              clients={clients}
              loading={loading}
              onView={handleViewClient}
              onEdit={handleEditClient}
              onDelete={handleDeleteClient}
              onViewProjects={handleViewProjects}
              onViewContracts={handleViewContracts}
              onViewInvoices={handleViewInvoices}
              onViewPayments={handleViewPayments}
            />
          </div>
        </div>
      ) : (
        <ClientGrid
          clients={clients}
          loading={loading}
          onView={handleViewClient}
          onEdit={handleEditClient}
          onDelete={handleDeleteClient}
          onViewProjects={handleViewProjects}
          onViewContracts={handleViewContracts}
          onViewInvoices={handleViewInvoices}
          onViewPayments={handleViewPayments}
        />
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      <ClientProfileModal
        client={selectedClient}
        isOpen={profileModalOpen}
        onClose={() => {
          setProfileModalOpen(false)
          setSelectedClient(null)
        }}
        onEdit={handleEditClient}
        initialTab={profileModalInitialTab}
      />

      <ContractModal
        isOpen={contractModalOpen}
        onClose={() => {
          setContractModalOpen(false)
          setSelectedClient(null)
          setSelectedContract(null)
        }}
        onSave={handleSaveContract}
        client={selectedClient}
        contract={selectedContract}
        mode={contractModalMode}
      />

      <ClientFormModal
        isOpen={clientFormModalOpen}
        onClose={() => {
          setClientFormModalOpen(false)
          setSelectedClient(null)
        }}
        onSave={handleSaveClient}
        client={selectedClient}
        mode={clientFormMode}
      />
    </div>
  )
}
