'use client'

import React, { useState, useEffect } from 'react'
import {
  XMarkIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

interface Contract {
  id?: string
  clientId: string
  name: string
  status: string
  value: number
  serviceType?: string
  language?: string
  description?: string
  currency?: string
  billingType?: string
  nextBillDate?: string
  signMethod?: string
  signedDate?: string
  executedDate?: string
  expiryDate?: string
}

interface Client {
  id: string
  companyName: string
  contactName: string
}

interface ContractModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (contract: Contract) => void
  client: Client | null
  contract?: Contract | null
  mode: 'create' | 'edit' | 'view'
}

const contractStatuses = [
  'Draft',
  'Pending Review',
  'Under Negotiation',
  'Approved',
  'Signed',
  'Active',
  'Completed',
  'Cancelled',
  'Expired'
]

const serviceTypes = [
  'Web Development',
  'Mobile App Development',
  'Software Development',
  'UI/UX Design',
  'Digital Marketing',
  'SEO Services',
  'Consulting',
  'Maintenance & Support',
  'Other'
]

const billingTypes = [
  'One-time',
  'Monthly',
  'Quarterly',
  'Annually',
  'Milestone-based',
  'Hourly'
]

const currencies = [
  'USD',
  'EUR',
  'GBP',
  'CAD',
  'AUD'
]

const signMethods = [
  'Electronic Signature',
  'Physical Signature',
  'Digital Certificate',
  'Verbal Agreement'
]

export default function ContractModal({
  isOpen,
  onClose,
  onSave,
  client,
  contract,
  mode
}: ContractModalProps) {
  const [formData, setFormData] = useState<Contract>({
    clientId: '',
    name: '',
    status: 'Draft',
    value: 0,
    serviceType: '',
    language: 'English',
    description: '',
    currency: 'USD',
    billingType: 'One-time',
    nextBillDate: '',
    signMethod: '',
    signedDate: '',
    executedDate: '',
    expiryDate: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      if (contract && mode !== 'create') {
        setFormData({
          ...contract,
          clientId: contract.clientId || client?.id || ''
        })
      } else if (client) {
        setFormData(prev => ({
          ...prev,
          clientId: client.id,
          name: `${client.companyName} - Service Agreement`
        }))
      }
      setErrors({})
    }
  }, [isOpen, contract, client, mode])

  const handleInputChange = (field: keyof Contract, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Contract name is required'
    }

    if (!formData.status) {
      newErrors.status = 'Status is required'
    }

    if (formData.value <= 0) {
      newErrors.value = 'Contract value must be greater than 0'
    }

    if (!formData.serviceType) {
      newErrors.serviceType = 'Service type is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving contract:', error)
    } finally {
      setLoading(false)
    }
  }

  const isReadOnly = mode === 'view'

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {mode === 'create' ? 'Create Contract' : mode === 'edit' ? 'Edit Contract' : 'Contract Details'}
                  </h3>
                  {client && (
                    <p className="text-sm text-gray-500">for {client.companyName}</p>
                  )}
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="bg-white px-6 py-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contract Name */}
              <div className="md:col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Contract Name *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  } ${errors.name ? 'border-red-300' : ''}`}
                  placeholder="Enter contract name"
                />
                {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
              </div>

              {/* Status */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Status *
                </label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  } ${errors.status ? 'border-red-300' : ''}`}
                >
                  {contractStatuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>
                {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
              </div>

              {/* Service Type */}
              <div>
                <label htmlFor="serviceType" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Type *
                </label>
                <select
                  id="serviceType"
                  value={formData.serviceType}
                  onChange={(e) => handleInputChange('serviceType', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  } ${errors.serviceType ? 'border-red-300' : ''}`}
                >
                  <option value="">Select service type</option>
                  {serviceTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                {errors.serviceType && <p className="mt-1 text-sm text-red-600">{errors.serviceType}</p>}
              </div>

              {/* Contract Value */}
              <div>
                <label htmlFor="value" className="block text-sm font-medium text-gray-700 mb-2">
                  Contract Value *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="value"
                    value={formData.value}
                    onChange={(e) => handleInputChange('value', parseFloat(e.target.value) || 0)}
                    disabled={isReadOnly}
                    className={`w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                      isReadOnly ? 'bg-gray-50' : ''
                    } ${errors.value ? 'border-red-300' : ''}`}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                </div>
                {errors.value && <p className="mt-1 text-sm text-red-600">{errors.value}</p>}
              </div>

              {/* Currency */}
              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  id="currency"
                  value={formData.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                >
                  {currencies.map((currency) => (
                    <option key={currency} value={currency}>
                      {currency}
                    </option>
                  ))}
                </select>
              </div>

              {/* Billing Type */}
              <div>
                <label htmlFor="billingType" className="block text-sm font-medium text-gray-700 mb-2">
                  Billing Type
                </label>
                <select
                  id="billingType"
                  value={formData.billingType}
                  onChange={(e) => handleInputChange('billingType', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                >
                  {billingTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              {/* Language */}
              <div>
                <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
                  Language
                </label>
                <input
                  type="text"
                  id="language"
                  value={formData.language}
                  onChange={(e) => handleInputChange('language', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                  placeholder="English"
                />
              </div>

              {/* Sign Method */}
              <div>
                <label htmlFor="signMethod" className="block text-sm font-medium text-gray-700 mb-2">
                  Signing Method
                </label>
                <select
                  id="signMethod"
                  value={formData.signMethod}
                  onChange={(e) => handleInputChange('signMethod', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                >
                  <option value="">Select signing method</option>
                  {signMethods.map((method) => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>

              {/* Signed Date */}
              <div>
                <label htmlFor="signedDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Signed Date
                </label>
                <input
                  type="date"
                  id="signedDate"
                  value={formData.signedDate ? formData.signedDate.split('T')[0] : ''}
                  onChange={(e) => handleInputChange('signedDate', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                />
              </div>

              {/* Executed Date */}
              <div>
                <label htmlFor="executedDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Executed Date
                </label>
                <input
                  type="date"
                  id="executedDate"
                  value={formData.executedDate ? formData.executedDate.split('T')[0] : ''}
                  onChange={(e) => handleInputChange('executedDate', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                />
              </div>

              {/* Expiry Date */}
              <div>
                <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Expiry Date
                </label>
                <input
                  type="date"
                  id="expiryDate"
                  value={formData.expiryDate ? formData.expiryDate.split('T')[0] : ''}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                />
              </div>

              {/* Next Bill Date */}
              <div>
                <label htmlFor="nextBillDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Next Bill Date
                </label>
                <input
                  type="date"
                  id="nextBillDate"
                  value={formData.nextBillDate ? formData.nextBillDate.split('T')[0] : ''}
                  onChange={(e) => handleInputChange('nextBillDate', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                />
              </div>

              {/* Description */}
              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                    isReadOnly ? 'bg-gray-50' : ''
                  }`}
                  placeholder="Enter contract description..."
                />
              </div>
            </div>

            {/* Footer */}
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {isReadOnly ? 'Close' : 'Cancel'}
              </button>
              {!isReadOnly && (
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      {mode === 'create' ? 'Create Contract' : 'Save Changes'}
                    </>
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
