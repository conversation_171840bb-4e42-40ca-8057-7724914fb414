'use client'

import { useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface Client {
  id: number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: number
  name: string
  status: string
}

interface ContractFormData {
  name: string
  description?: string
  clientId?: number
  projectId?: number
  value?: number
  status: string
  serviceType?: string
  startDate?: string
  endDate?: string
  terms?: string
  notes?: string
  isActive: boolean
}

interface ContractFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: ContractFormData) => Promise<void>
  title: string
  initialData?: Partial<ContractFormData>
  preSelectedClientId?: number
  mode?: 'create' | 'edit'
}

const CONTRACT_STATUSES = [
  'DRAFT',
  'PENDING',
  'ACTIVE',
  'COMPLETED',
  'CANCELLED',
  'EXPIRED'
]

const SERVICE_TYPES = [
  'WEB_DEVELOPMENT',
  'MOBILE_DEVELOPMENT',
  'CONSULTING',
  'MAINTENANCE',
  'DESIGN',
  'MARKETING',
  'OTHER'
]

export default function ContractForm({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  preSelectedClientId,
  mode = 'create'
}: ContractFormProps) {
  const [formData, setFormData] = useState<ContractFormData>({
    name: '',
    description: '',
    clientId: preSelectedClientId,
    projectId: undefined,
    value: 0,
    status: 'DRAFT',
    serviceType: '',
    startDate: '',
    endDate: '',
    terms: '',
    notes: '',
    isActive: true,
    ...initialData
  })

  const [clients, setClients] = useState<Client[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Fetch clients
  useEffect(() => {
    if (isOpen) {
      fetchClients()
    }
  }, [isOpen])

  // Fetch projects when client is selected
  useEffect(() => {
    if (formData.clientId) {
      fetchProjectsForClient(formData.clientId)
    }
  }, [formData.clientId])

  const fetchClients = async () => {
    try {
      const response = await fetch('/api/admin/clients?limit=100')
      if (response.ok) {
        const data = await response.json()
        setClients(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
    }
  }

  const fetchProjectsForClient = async (clientId: number) => {
    try {
      const response = await fetch(`/api/admin/projects?clientId=${clientId}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setProjects(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? (value === '' ? undefined : Number(value))
              : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Contract name is required'
    }

    if (!formData.status) {
      newErrors.status = 'Contract status is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
      onClose()
      // Reset form
      setFormData({
        name: '',
        description: '',
        clientId: preSelectedClientId,
        projectId: undefined,
        value: 0,
        status: 'DRAFT',
        serviceType: '',
        startDate: '',
        endDate: '',
        terms: '',
        notes: '',
        isActive: true
      })
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex justify-between items-center mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    {title}
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Basic Information</h4>
                      
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                          Contract Name *
                        </label>
                        <input
                          type="text"
                          name="name"
                          id="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.name ? 'border-red-300' : ''
                          }`}
                          placeholder="Enter contract name"
                        />
                        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                      </div>

                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <textarea
                          name="description"
                          id="description"
                          rows={3}
                          value={formData.description}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Enter contract description"
                        />
                      </div>

                      <div>
                        <label htmlFor="value" className="block text-sm font-medium text-gray-700">
                          Contract Value ($)
                        </label>
                        <input
                          type="number"
                          name="value"
                          id="value"
                          value={formData.value || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="0.00"
                          step="0.01"
                        />
                      </div>
                    </div>

                    {/* Relationships and Status */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Relationships & Status</h4>
                      
                      <div>
                        <label htmlFor="clientId" className="block text-sm font-medium text-gray-700">
                          Client
                        </label>
                        <select
                          name="clientId"
                          id="clientId"
                          value={formData.clientId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          disabled={!!preSelectedClientId}
                        >
                          <option value="">Select a client</option>
                          {clients.map((client) => (
                            <option key={client.id} value={client.id}>
                              {client.companyName} - {client.contactName}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label htmlFor="projectId" className="block text-sm font-medium text-gray-700">
                          Related Project
                        </label>
                        <select
                          name="projectId"
                          id="projectId"
                          value={formData.projectId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          <option value="">Select a project</option>
                          {projects.map((project) => (
                            <option key={project.id} value={project.id}>
                              {project.name} ({project.status})
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                          Status *
                        </label>
                        <select
                          name="status"
                          id="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.status ? 'border-red-300' : ''
                          }`}
                        >
                          {CONTRACT_STATUSES.map((status) => (
                            <option key={status} value={status}>
                              {status.replace('_', ' ')}
                            </option>
                          ))}
                        </select>
                        {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
                      </div>

                      <div>
                        <label htmlFor="serviceType" className="block text-sm font-medium text-gray-700">
                          Service Type
                        </label>
                        <select
                          name="serviceType"
                          id="serviceType"
                          value={formData.serviceType}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          <option value="">Select service type</option>
                          {SERVICE_TYPES.map((type) => (
                            <option key={type} value={type}>
                              {type.replace('_', ' ')}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Dates */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                        Start Date
                      </label>
                      <input
                        type="date"
                        name="startDate"
                        id="startDate"
                        value={formData.startDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                        End Date
                      </label>
                      <input
                        type="date"
                        name="endDate"
                        id="endDate"
                        value={formData.endDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Terms and Notes */}
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="terms" className="block text-sm font-medium text-gray-700">
                        Contract Terms
                      </label>
                      <textarea
                        name="terms"
                        id="terms"
                        rows={4}
                        value={formData.terms}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter contract terms and conditions"
                      />
                    </div>

                    <div>
                      <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                        Notes
                      </label>
                      <textarea
                        name="notes"
                        id="notes"
                        rows={3}
                        value={formData.notes}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Additional notes"
                      />
                    </div>
                  </div>

                  {/* Active Status */}
                  <div className="flex items-center">
                    <input
                      id="isActive"
                      name="isActive"
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                      Active Contract
                    </label>
                  </div>

                  <div className="flex justify-end space-x-3 pt-6 border-t">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {loading ? 'Saving...' : mode === 'create' ? 'Create Contract' : 'Update Contract'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
