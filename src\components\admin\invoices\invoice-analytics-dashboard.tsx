'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Donut<PERSON><PERSON> } from '@/components/admin/charts'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline'

interface AnalyticsData {
  summary: {
    totalInvoices: number
    totalOutstanding: number
    totalPaid: number
    overdueInvoices: number
    upcomingPayments: number
    averagePaymentTime: number
    totalRevenue: number
  }
  trends: {
    monthly: {
      invoices: Array<{ month: string; count: number; amount: number }>
      payments: Array<{ month: string; count: number; amount: number }>
    }
  }
  distribution: {
    status: Array<{ status: string; count: number; amount: number }>
    paymentMethods: Array<{ method: string; count: number; amount: number }>
  }
  topClients: Array<{
    id: string
    companyName: string
    contactName: string
    invoiceCount: number
    totalRevenue: number
    paidRevenue: number
  }>
  recent: {
    invoices: Array<any>
    payments: Array<any>
  }
}

interface InvoiceAnalyticsDashboardProps {
  className?: string
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const formatMonth = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    year: '2-digit'
  })
}

export default function InvoiceAnalyticsDashboard({ className = '' }: InvoiceAnalyticsDashboardProps) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/invoices/analytics')
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data')
      }

      const result = await response.json()
      if (result.success) {
        setAnalyticsData(result.data)
      } else {
        throw new Error(result.message || 'Failed to fetch analytics')
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch analytics')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg border p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-48 bg-gray-100 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error || !analyticsData) {
    return (
      <div className={`bg-white rounded-lg border p-6 ${className}`}>
        <div className="text-center py-8">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Analytics Unavailable</h3>
          <p className="mt-1 text-sm text-gray-500">
            {error || 'Unable to load analytics data at this time.'}
          </p>
          <button
            onClick={fetchAnalytics}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  // Prepare chart data
  const monthlyInvoiceData = analyticsData.trends.monthly.invoices.map(item => ({
    label: formatMonth(item.month),
    value: item.amount,
    date: new Date(item.month)
  }))

  const monthlyPaymentData = analyticsData.trends.monthly.payments.map(item => ({
    label: formatMonth(item.month),
    value: item.amount,
    date: new Date(item.month)
  }))

  const statusDistributionData = analyticsData.distribution.status.map((item, index) => ({
    label: item.status,
    value: item.amount,
    color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5]
  }))

  const paymentMethodData = analyticsData.distribution.paymentMethods.map((item, index) => ({
    label: item.method.replace('_', ' '),
    value: item.amount,
    color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'][index % 6]
  }))

  const topClientsBarData = analyticsData.topClients.slice(0, 5).map(client => ({
    label: client.companyName.length > 15 
      ? client.companyName.substring(0, 15) + '...' 
      : client.companyName,
    value: client.totalRevenue
  }))

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Invoice Analytics</h3>
        <button
          onClick={fetchAnalytics}
          className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          Refresh Data
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Invoice Trends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <LineChart
            data={monthlyInvoiceData}
            title="Monthly Invoice Revenue"
            height={250}
            color="#3B82F6"
            formatValue={formatCurrency}
            className="h-full"
          />
        </motion.div>

        {/* Monthly Payment Trends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <LineChart
            data={monthlyPaymentData}
            title="Monthly Payment Collection"
            height={250}
            color="#10B981"
            formatValue={formatCurrency}
            className="h-full"
          />
        </motion.div>

        {/* Invoice Status Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <DonutChart
            data={statusDistributionData}
            title="Invoice Status Distribution"
            size={250}
            formatValue={formatCurrency}
            className="h-full"
          />
        </motion.div>

        {/* Payment Methods */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <DonutChart
            data={paymentMethodData}
            title="Payment Methods"
            size={250}
            formatValue={formatCurrency}
            className="h-full"
          />
        </motion.div>

        {/* Top Clients */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="lg:col-span-2"
        >
          <BarChart
            data={topClientsBarData}
            title="Top 5 Clients by Revenue"
            height={300}
            color="#8B5CF6"
            formatValue={formatCurrency}
            className="h-full"
          />
        </motion.div>
      </div>

      {/* Top Clients Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-lg border"
      >
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-lg font-medium text-gray-900 flex items-center">
            <UserGroupIcon className="h-5 w-5 mr-2" />
            Top Clients by Revenue
          </h4>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoices
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paid Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Collection Rate
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analyticsData.topClients.slice(0, 10).map((client, index) => {
                const collectionRate = client.totalRevenue > 0 
                  ? (client.paidRevenue / client.totalRevenue) * 100 
                  : 0

                return (
                  <tr key={client.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {client.companyName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {client.contactName}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {client.invoiceCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(client.totalRevenue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                      {formatCurrency(client.paidRevenue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                          <div 
                            className={`h-2 rounded-full ${
                              collectionRate >= 80 ? 'bg-green-500' :
                              collectionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${Math.min(collectionRate, 100)}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600">
                          {collectionRate.toFixed(0)}%
                        </span>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </motion.div>
    </div>
  )
}
