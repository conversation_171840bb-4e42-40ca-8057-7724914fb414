'use client'

import { useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface Client {
  id: number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: number
  name: string
  status: string
}

interface Contract {
  id: number
  name: string
  status: string
  value: number
}

interface InvoiceFormData {
  invoiceNumber: string
  clientId?: number
  projectId?: number
  contractId?: number
  totalAmount?: number
  taxAmount?: number
  discountAmount?: number
  status: string
  issueDate?: string
  dueDate?: string
  paidDate?: string
  description?: string
  notes?: string
  paymentTerms?: string
  isRecurring: boolean
}

interface InvoiceFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: InvoiceFormData) => Promise<void>
  title: string
  initialData?: Partial<InvoiceFormData>
  preSelectedClientId?: number
  mode?: 'create' | 'edit'
}

const INVOICE_STATUSES = [
  'DRAFT',
  'SENT',
  'VIEWED',
  'PARTIAL',
  'PAID',
  'OVERDUE',
  'CANCELLED'
]

export default function InvoiceForm({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  preSelectedClientId,
  mode = 'create'
}: InvoiceFormProps) {
  const [formData, setFormData] = useState<InvoiceFormData>({
    invoiceNumber: '',
    clientId: preSelectedClientId,
    projectId: undefined,
    contractId: undefined,
    totalAmount: 0,
    taxAmount: 0,
    discountAmount: 0,
    status: 'DRAFT',
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    paidDate: '',
    description: '',
    notes: '',
    paymentTerms: '',
    isRecurring: false,
    ...initialData
  })

  const [clients, setClients] = useState<Client[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [contracts, setContracts] = useState<Contract[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Fetch clients
  useEffect(() => {
    if (isOpen) {
      fetchClients()
      generateInvoiceNumber()
    }
  }, [isOpen])

  // Fetch projects and contracts when client is selected
  useEffect(() => {
    if (formData.clientId) {
      fetchProjectsForClient(formData.clientId)
      fetchContractsForClient(formData.clientId)
    }
  }, [formData.clientId])

  const generateInvoiceNumber = () => {
    if (mode === 'create' && !formData.invoiceNumber) {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const invoiceNumber = `INV-${year}${month}-${random}`
      
      setFormData(prev => ({ ...prev, invoiceNumber }))
    }
  }

  const fetchClients = async () => {
    try {
      const response = await fetch('/api/admin/clients?limit=100')
      if (response.ok) {
        const data = await response.json()
        setClients(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
    }
  }

  const fetchProjectsForClient = async (clientId: number) => {
    try {
      const response = await fetch(`/api/admin/projects?clientId=${clientId}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setProjects(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    }
  }

  const fetchContractsForClient = async (clientId: number) => {
    try {
      const response = await fetch(`/api/admin/contracts?clientId=${clientId}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setContracts(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching contracts:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? (value === '' ? undefined : Number(value))
              : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.invoiceNumber.trim()) {
      newErrors.invoiceNumber = 'Invoice number is required'
    }

    if (!formData.status) {
      newErrors.status = 'Invoice status is required'
    }

    if (!formData.totalAmount || formData.totalAmount <= 0) {
      newErrors.totalAmount = 'Total amount must be greater than 0'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
      onClose()
      // Reset form
      setFormData({
        invoiceNumber: '',
        clientId: preSelectedClientId,
        projectId: undefined,
        contractId: undefined,
        totalAmount: 0,
        taxAmount: 0,
        discountAmount: 0,
        status: 'DRAFT',
        issueDate: new Date().toISOString().split('T')[0],
        dueDate: '',
        paidDate: '',
        description: '',
        notes: '',
        paymentTerms: '',
        isRecurring: false
      })
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex justify-between items-center mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    {title}
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Invoice Details</h4>
                      
                      <div>
                        <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700">
                          Invoice Number *
                        </label>
                        <input
                          type="text"
                          name="invoiceNumber"
                          id="invoiceNumber"
                          value={formData.invoiceNumber}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.invoiceNumber ? 'border-red-300' : ''
                          }`}
                          placeholder="INV-202412-001"
                        />
                        {errors.invoiceNumber && <p className="mt-1 text-sm text-red-600">{errors.invoiceNumber}</p>}
                      </div>

                      <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                          Status *
                        </label>
                        <select
                          name="status"
                          id="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.status ? 'border-red-300' : ''
                          }`}
                        >
                          {INVOICE_STATUSES.map((status) => (
                            <option key={status} value={status}>
                              {status.replace('_', ' ')}
                            </option>
                          ))}
                        </select>
                        {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
                      </div>

                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <textarea
                          name="description"
                          id="description"
                          rows={3}
                          value={formData.description}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Invoice description"
                        />
                      </div>
                    </div>

                    {/* Relationships */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Relationships</h4>
                      
                      <div>
                        <label htmlFor="clientId" className="block text-sm font-medium text-gray-700">
                          Client
                        </label>
                        <select
                          name="clientId"
                          id="clientId"
                          value={formData.clientId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          disabled={!!preSelectedClientId}
                        >
                          <option value="">Select a client</option>
                          {clients.map((client) => (
                            <option key={client.id} value={client.id}>
                              {client.companyName} - {client.contactName}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label htmlFor="projectId" className="block text-sm font-medium text-gray-700">
                          Related Project
                        </label>
                        <select
                          name="projectId"
                          id="projectId"
                          value={formData.projectId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          <option value="">Select a project</option>
                          {projects.map((project) => (
                            <option key={project.id} value={project.id}>
                              {project.name} ({project.status})
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label htmlFor="contractId" className="block text-sm font-medium text-gray-700">
                          Related Contract
                        </label>
                        <select
                          name="contractId"
                          id="contractId"
                          value={formData.contractId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          <option value="">Select a contract</option>
                          {contracts.map((contract) => (
                            <option key={contract.id} value={contract.id}>
                              {contract.name} (${contract.value})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Financial Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="totalAmount" className="block text-sm font-medium text-gray-700">
                        Total Amount ($) *
                      </label>
                      <input
                        type="number"
                        name="totalAmount"
                        id="totalAmount"
                        value={formData.totalAmount || ''}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                          errors.totalAmount ? 'border-red-300' : ''
                        }`}
                        placeholder="0.00"
                        step="0.01"
                      />
                      {errors.totalAmount && <p className="mt-1 text-sm text-red-600">{errors.totalAmount}</p>}
                    </div>

                    <div>
                      <label htmlFor="taxAmount" className="block text-sm font-medium text-gray-700">
                        Tax Amount ($)
                      </label>
                      <input
                        type="number"
                        name="taxAmount"
                        id="taxAmount"
                        value={formData.taxAmount || ''}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="0.00"
                        step="0.01"
                      />
                    </div>

                    <div>
                      <label htmlFor="discountAmount" className="block text-sm font-medium text-gray-700">
                        Discount Amount ($)
                      </label>
                      <input
                        type="number"
                        name="discountAmount"
                        id="discountAmount"
                        value={formData.discountAmount || ''}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="0.00"
                        step="0.01"
                      />
                    </div>
                  </div>

                  {/* Dates */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700">
                        Issue Date
                      </label>
                      <input
                        type="date"
                        name="issueDate"
                        id="issueDate"
                        value={formData.issueDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700">
                        Due Date
                      </label>
                      <input
                        type="date"
                        name="dueDate"
                        id="dueDate"
                        value={formData.dueDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label htmlFor="paidDate" className="block text-sm font-medium text-gray-700">
                        Paid Date
                      </label>
                      <input
                        type="date"
                        name="paidDate"
                        id="paidDate"
                        value={formData.paidDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Terms and Notes */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="paymentTerms" className="block text-sm font-medium text-gray-700">
                        Payment Terms
                      </label>
                      <textarea
                        name="paymentTerms"
                        id="paymentTerms"
                        rows={3}
                        value={formData.paymentTerms}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Payment terms and conditions"
                      />
                    </div>

                    <div>
                      <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                        Notes
                      </label>
                      <textarea
                        name="notes"
                        id="notes"
                        rows={3}
                        value={formData.notes}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Additional notes"
                      />
                    </div>
                  </div>

                  {/* Recurring Invoice */}
                  <div className="flex items-center">
                    <input
                      id="isRecurring"
                      name="isRecurring"
                      type="checkbox"
                      checked={formData.isRecurring}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-900">
                      Recurring Invoice
                    </label>
                  </div>

                  <div className="flex justify-end space-x-3 pt-6 border-t">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {loading ? 'Saving...' : mode === 'create' ? 'Create Invoice' : 'Update Invoice'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
