'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import InvoiceGrid from './invoice-grid'
import InvoiceTable from './invoice-table'
import InvoiceForm from './invoice-form'
import InvoiceViewModal from './invoice-view-modal'
import AddPaymentModal from './add-payment-modal'
import InvoiceSummaryCards from './invoice-summary-cards'
import InvoiceAnalyticsDashboard from './invoice-analytics-dashboard'

interface Invoice {
  id: string
  invoiceNumber: string
  client: {
    companyName: string
    contactName: string
    contactEmail: string
  }
  project?: {
    name: string
    status: string
  }
  totalAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  payments: Array<{
    id: string
    amount: number
    status: string
    paidAt?: string
  }>
}

interface Stats {
  total: number
  draft: number
  sent: number
  paid: number
  overdue: number
  totalAmount: number
  paidAmount: number
  pendingAmount: number
}

export default function InvoiceManager() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [stats, setStats] = useState<Stats>({
    total: 0,
    draft: 0,
    sent: 0,
    paid: 0,
    overdue: 0,
    totalAmount: 0,
    paidAmount: 0,
    pendingAmount: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid')
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [analyticsData, setAnalyticsData] = useState(null)

  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { filter: statusFilter })
      })

      const response = await fetch(`/api/admin/invoices?${params}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error Response:', response.status, response.statusText, errorText)
        throw new Error(`Failed to fetch invoices: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log('API Response:', result)

      if (result.success) {
        setInvoices(result.data || [])
        setTotalPages(Math.ceil((result.total || 0) / 12))
        
        // Calculate stats
        const newStats = result.data.reduce((acc: Stats, invoice: Invoice) => {
          acc.total++
          acc[invoice.status.toLowerCase() as keyof Stats]++
          acc.totalAmount += invoice.totalAmount
          
          if (invoice.status === 'PAID') {
            acc.paidAmount += invoice.totalAmount
          } else {
            acc.pendingAmount += invoice.totalAmount
          }
          
          return acc
        }, {
          total: 0,
          draft: 0,
          sent: 0,
          paid: 0,
          overdue: 0,
          totalAmount: 0,
          paidAmount: 0,
          pendingAmount: 0
        })
        
        setStats(newStats)
      } else {
        console.error('API returned unsuccessful response:', result)
        throw new Error(result.message || 'Failed to fetch invoices')
      }
    } catch (error) {
      console.error('Error fetching invoices:', error)
      // Set empty data on error to prevent UI crashes
      setInvoices([])
      setStats({
        total: 0,
        draft: 0,
        sent: 0,
        paid: 0,
        overdue: 0,
        totalAmount: 0,
        paidAmount: 0,
        pendingAmount: 0
      })
    } finally {
      setLoading(false)
    }
  }, [page, searchTerm, statusFilter])

  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setPage(1)
  }

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status)
    setPage(1)
  }

  const handleView = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setShowViewModal(true)
  }

  const handleEdit = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setShowEditModal(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this invoice?')) return

    try {
      const response = await fetch(`/api/admin/invoices/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete invoice')

      await fetchInvoices()
    } catch (error) {
      console.error('Error deleting invoice:', error)
      alert('Failed to delete invoice')
    }
  }

  const handleDownload = async (invoice: Invoice) => {
    // TODO: Implement PDF download
    console.log('Download invoice:', invoice.invoiceNumber)
  }

  const handleSend = async (invoice: Invoice) => {
    try {
      const response = await fetch(`/api/admin/invoices/${invoice.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'SENT' })
      })

      if (!response.ok) throw new Error('Failed to send invoice')

      await fetchInvoices()
    } catch (error) {
      console.error('Error sending invoice:', error)
      alert('Failed to send invoice')
    }
  }

  const handleMarkPaid = async (invoice: Invoice) => {
    try {
      const response = await fetch(`/api/admin/invoices/${invoice.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: 'PAID',
          paidAt: new Date().toISOString()
        })
      })

      if (!response.ok) throw new Error('Failed to mark invoice as paid')

      await fetchInvoices()
    } catch (error) {
      console.error('Error marking invoice as paid:', error)
      alert('Failed to mark invoice as paid')
    }
  }

  const handleAddPayment = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setShowPaymentModal(true)
  }

  const handleCreateSuccess = () => {
    setShowCreateModal(false)
    fetchInvoices()
  }

  const handleEditSuccess = () => {
    setShowEditModal(false)
    setSelectedInvoice(null)
    fetchInvoices()
  }

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false)
    setSelectedInvoice(null)
    fetchInvoices()
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage client invoices, billing, and payment tracking
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowAnalytics(!showAnalytics)}
            className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium transition-colors ${
              showAnalytics
                ? 'bg-blue-50 text-blue-700 border-blue-300'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            Analytics
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
            Create Invoice
          </button>
        </div>
      </div>

      {/* Enhanced Summary Cards */}
      <InvoiceSummaryCards
        data={{
          totalOutstanding: stats.pendingAmount,
          totalPaid: stats.paidAmount,
          overdueInvoices: stats.overdue,
          upcomingPayments: 0, // This would come from analytics API
          averagePaymentTime: 25, // This would come from analytics API
          totalRevenue: stats.totalAmount,
          totalInvoices: stats.total
        }}
        loading={loading}
      />

      {/* Filters and Controls */}
      <div className="bg-white shadow rounded-lg">
        <div className="p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Search invoices by number, client, or description..."
                />
              </div>
            </div>

            {/* Filters and View Toggle */}
            <div className="flex items-center space-x-3">
              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => handleStatusFilter(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="DRAFT">Draft</option>
                <option value="SENT">Sent</option>
                <option value="PAID">Paid</option>
                <option value="OVERDUE">Overdue</option>
                <option value="CANCELLED">Cancelled</option>
              </select>

              {/* View Toggle */}
              <div className="flex rounded-md shadow-sm">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`relative inline-flex items-center px-3 py-2 rounded-l-md border text-sm font-medium ${
                    viewMode === 'grid'
                      ? 'bg-blue-50 border-blue-500 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <Squares2X2Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`relative inline-flex items-center px-3 py-2 rounded-r-md border-t border-r border-b text-sm font-medium ${
                    viewMode === 'table'
                      ? 'bg-blue-50 border-blue-500 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <ListBulletIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Export Button */}
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="bg-white shadow rounded-lg">
        <div className="p-6">
          {viewMode === 'grid' ? (
            <InvoiceGrid
              invoices={invoices}
              loading={loading}
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onDownload={handleDownload}
              onSend={handleSend}
              onMarkPaid={handleMarkPaid}
              onAddPayment={handleAddPayment}
            />
          ) : (
            <InvoiceTable
              invoices={invoices}
              loading={loading}
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onDownload={handleDownload}
              onSend={handleSend}
              onMarkPaid={handleMarkPaid}
              onAddPayment={handleAddPayment}
            />
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setPage(Math.min(totalPages, page + 1))}
                disabled={page === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{page}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setPage(Math.max(1, page - 1))}
                    disabled={page === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setPage(Math.min(totalPages, page + 1))}
                    disabled={page === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Analytics Dashboard */}
      {showAnalytics && (
        <InvoiceAnalyticsDashboard />
      )}

      {/* Modals */}
      {showCreateModal && (
        <InvoiceForm
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateSuccess}
          title="Create Invoice"
          mode="create"
        />
      )}

      {showEditModal && selectedInvoice && (
        <InvoiceForm
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false)
            setSelectedInvoice(null)
          }}
          onSubmit={handleEditSuccess}
          title="Edit Invoice"
          mode="edit"
          initialData={selectedInvoice}
        />
      )}

      {showViewModal && selectedInvoice && (
        <InvoiceViewModal
          isOpen={showViewModal}
          onClose={() => {
            setShowViewModal(false)
            setSelectedInvoice(null)
          }}
          invoice={selectedInvoice}
        />
      )}

      {showPaymentModal && selectedInvoice && (
        <AddPaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false)
            setSelectedInvoice(null)
          }}
          onSuccess={handlePaymentSuccess}
          invoice={selectedInvoice}
        />
      )}
    </div>
  )
}
