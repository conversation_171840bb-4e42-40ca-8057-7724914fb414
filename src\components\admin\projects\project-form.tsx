'use client'

import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface Client {
  id: number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Order {
  id: number
  orderTitle: string
  status: string
  totalAmount: number
}

interface TeamMember {
  id: number
  name: string
  position: string
}

interface ProjectFormData {
  name: string
  description: string
  goals?: string
  clientId?: number
  orderId: number
  managerId?: number
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  tags?: string
  startDate?: string
  completionDate?: string
  estimatedCost?: number
  estimatedTime?: string
  estimatedEffort?: string
  status: string
  isFeatured: boolean
  isPublic: boolean
  displayOrder: number
}

interface ProjectFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: ProjectFormData) => Promise<void>
  title: string
  initialData?: Partial<ProjectFormData>
  preSelectedClientId?: number
  mode?: 'create' | 'edit'
}

const PROJECT_STATUSES = [
  'PLANNING',
  'IN_PROGRESS', 
  'COMPLETED',
  'ON_HOLD',
  'CANCELLED'
]

export default function ProjectForm({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  preSelectedClientId,
  mode = 'create'
}: ProjectFormProps) {
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    goals: '',
    clientId: preSelectedClientId,
    orderId: 0,
    managerId: undefined,
    imageUrl: '',
    projectUrl: '',
    githubUrl: '',
    tags: '',
    startDate: '',
    completionDate: '',
    estimatedCost: 0,
    estimatedTime: '',
    estimatedEffort: '',
    status: 'PLANNING',
    isFeatured: false,
    isPublic: true,
    displayOrder: 0,
    ...initialData
  })

  const [clients, setClients] = useState<Client[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Fetch clients, orders, and team members
  useEffect(() => {
    if (isOpen) {
      fetchClients()
      fetchTeamMembers()
    }
  }, [isOpen])

  // Fetch orders when client is selected
  useEffect(() => {
    if (formData.clientId) {
      fetchOrdersForClient(formData.clientId)
    }
  }, [formData.clientId])

  const fetchClients = async () => {
    try {
      const response = await fetch('/api/admin/clients?limit=100')
      if (response.ok) {
        const data = await response.json()
        setClients(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
    }
  }

  const fetchOrdersForClient = async (clientId: number) => {
    try {
      const response = await fetch(`/api/admin/orders?clientId=${clientId}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setOrders(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
    }
  }

  const fetchTeamMembers = async () => {
    try {
      const response = await fetch('/api/admin/team-members?limit=100')
      if (response.ok) {
        const data = await response.json()
        setTeamMembers(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching team members:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? (value === '' ? undefined : Number(value))
              : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Project name is required'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Project description is required'
    }

    if (!formData.orderId) {
      newErrors.orderId = 'Order is required'
    }

    if (!formData.status) {
      newErrors.status = 'Project status is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
      onClose()
      // Reset form
      setFormData({
        name: '',
        description: '',
        goals: '',
        clientId: preSelectedClientId,
        orderId: 0,
        managerId: undefined,
        imageUrl: '',
        projectUrl: '',
        githubUrl: '',
        tags: '',
        startDate: '',
        completionDate: '',
        estimatedCost: 0,
        estimatedTime: '',
        estimatedEffort: '',
        status: 'PLANNING',
        isFeatured: false,
        isPublic: true,
        displayOrder: 0
      })
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[60] overflow-y-auto" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose}></div>

        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto z-10">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {title}
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white px-6 py-6">
            <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Basic Information</h4>
                      
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                          Project Name *
                        </label>
                        <input
                          type="text"
                          name="name"
                          id="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.name ? 'border-red-300' : ''
                          }`}
                          placeholder="Enter project name"
                        />
                        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                      </div>

                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                          Description *
                        </label>
                        <textarea
                          name="description"
                          id="description"
                          rows={3}
                          value={formData.description}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.description ? 'border-red-300' : ''
                          }`}
                          placeholder="Enter project description"
                        />
                        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                      </div>

                      <div>
                        <label htmlFor="goals" className="block text-sm font-medium text-gray-700">
                          Project Goals
                        </label>
                        <textarea
                          name="goals"
                          id="goals"
                          rows={2}
                          value={formData.goals}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Enter project goals"
                        />
                      </div>
                    </div>

                    {/* Relationships */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Relationships</h4>
                      
                      <div>
                        <label htmlFor="clientId" className="block text-sm font-medium text-gray-700">
                          Client
                        </label>
                        <select
                          name="clientId"
                          id="clientId"
                          value={formData.clientId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          disabled={!!preSelectedClientId}
                        >
                          <option value="">Select a client</option>
                          {clients.map((client) => (
                            <option key={client.id} value={client.id}>
                              {client.companyName} - {client.contactName}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label htmlFor="orderId" className="block text-sm font-medium text-gray-700">
                          Order *
                        </label>
                        <select
                          name="orderId"
                          id="orderId"
                          value={formData.orderId || ''}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                            errors.orderId ? 'border-red-300' : ''
                          }`}
                        >
                          <option value="">Select an order</option>
                          {orders.map((order) => (
                            <option key={order.id} value={order.id}>
                              {order.orderTitle} - ${order.totalAmount}
                            </option>
                          ))}
                        </select>
                        {errors.orderId && <p className="mt-1 text-sm text-red-600">{errors.orderId}</p>}
                      </div>

                      <div>
                        <label htmlFor="managerId" className="block text-sm font-medium text-gray-700">
                          Project Manager
                        </label>
                        <select
                          name="managerId"
                          id="managerId"
                          value={formData.managerId || ''}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          <option value="">Select a project manager</option>
                          {teamMembers.map((member) => (
                            <option key={member.id} value={member.id}>
                              {member.name} - {member.position}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                        Status *
                      </label>
                      <select
                        name="status"
                        id="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                          errors.status ? 'border-red-300' : ''
                        }`}
                      >
                        {PROJECT_STATUSES.map((status) => (
                          <option key={status} value={status}>
                            {status.replace('_', ' ')}
                          </option>
                        ))}
                      </select>
                      {errors.status && <p className="mt-1 text-sm text-red-600">{errors.status}</p>}
                    </div>

                    <div>
                      <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                        Start Date
                      </label>
                      <input
                        type="date"
                        name="startDate"
                        id="startDate"
                        value={formData.startDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label htmlFor="completionDate" className="block text-sm font-medium text-gray-700">
                        Completion Date
                      </label>
                      <input
                        type="date"
                        name="completionDate"
                        id="completionDate"
                        value={formData.completionDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Estimates */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="estimatedCost" className="block text-sm font-medium text-gray-700">
                        Estimated Cost ($)
                      </label>
                      <input
                        type="number"
                        name="estimatedCost"
                        id="estimatedCost"
                        value={formData.estimatedCost || ''}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="0.00"
                        step="0.01"
                      />
                    </div>

                    <div>
                      <label htmlFor="estimatedTime" className="block text-sm font-medium text-gray-700">
                        Estimated Time
                      </label>
                      <input
                        type="text"
                        name="estimatedTime"
                        id="estimatedTime"
                        value={formData.estimatedTime}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="e.g., 3 months"
                      />
                    </div>

                    <div>
                      <label htmlFor="estimatedEffort" className="block text-sm font-medium text-gray-700">
                        Estimated Effort
                      </label>
                      <input
                        type="text"
                        name="estimatedEffort"
                        id="estimatedEffort"
                        value={formData.estimatedEffort}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="e.g., 500 hours"
                      />
                    </div>
                  </div>

                  {/* URLs and Media */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
                        Project Image URL
                      </label>
                      <input
                        type="url"
                        name="imageUrl"
                        id="imageUrl"
                        value={formData.imageUrl}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>

                    <div>
                      <label htmlFor="projectUrl" className="block text-sm font-medium text-gray-700">
                        Project URL
                      </label>
                      <input
                        type="url"
                        name="projectUrl"
                        id="projectUrl"
                        value={formData.projectUrl}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="https://project-demo.com"
                      />
                    </div>

                    <div>
                      <label htmlFor="githubUrl" className="block text-sm font-medium text-gray-700">
                        GitHub URL
                      </label>
                      <input
                        type="url"
                        name="githubUrl"
                        id="githubUrl"
                        value={formData.githubUrl}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="https://github.com/user/repo"
                      />
                    </div>
                  </div>

                  {/* Tags and Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                        Tags
                      </label>
                      <input
                        type="text"
                        name="tags"
                        id="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="React, TypeScript, Next.js (comma separated)"
                      />
                    </div>

                    <div>
                      <label htmlFor="displayOrder" className="block text-sm font-medium text-gray-700">
                        Display Order
                      </label>
                      <input
                        type="number"
                        name="displayOrder"
                        id="displayOrder"
                        value={formData.displayOrder}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="0"
                      />
                    </div>
                  </div>

                  {/* Checkboxes */}
                  <div className="flex space-x-6">
                    <div className="flex items-center">
                      <input
                        id="isFeatured"
                        name="isFeatured"
                        type="checkbox"
                        checked={formData.isFeatured}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
                        Featured Project
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="isPublic"
                        name="isPublic"
                        type="checkbox"
                        checked={formData.isPublic}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isPublic" className="ml-2 block text-sm text-gray-900">
                        Public Project
                      </label>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 pt-6 border-t">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {loading ? 'Saving...' : mode === 'create' ? 'Create Project' : 'Update Project'}
                    </button>
                  </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
