'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  optionId: string
  name: string
  description?: string
  cost?: number
  discountRate?: number
  totalDiscount?: number
  isIncluded: boolean
  createdAt: string
  updatedAt: string
  option?: {
    id: string
    name: string
    service?: {
      id: string
      name: string
      category?: {
        id: string
        name: string
      }
    }
  }
  _count?: {
    orderDetails: number
  }
}

interface OptionFeaturesManagementProps {
  option: ServiceOption
}

interface FeatureFormData {
  name: string
  description: string
  cost: number
  discountRate: number
  totalDiscount: number
  isIncluded: boolean
}

export function OptionFeaturesManagement({ option }: OptionFeaturesManagementProps) {
  const [features, setFeatures] = useState<OptionFeature[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingFeature, setEditingFeature] = useState<OptionFeature | null>(null)
  const [formData, setFormData] = useState<FeatureFormData>({
    name: '',
    description: '',
    cost: 0,
    discountRate: 0,
    totalDiscount: 0,
    isIncluded: true
  })

  useEffect(() => {
    if (option && option.id) {
      fetchFeatures()
    }
  }, [option.id])

  const fetchFeatures = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/service-option-features?optionId=${option.id}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setFeatures(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching option features:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingFeature 
        ? `/api/admin/service-option-features/${editingFeature.id}`
        : '/api/admin/service-option-features'
      
      const method = editingFeature ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          optionId: option.id,
          name: formData.name,
          description: formData.description,
          cost: formData.cost,
          discountRate: formData.discountRate,
          totalDiscount: formData.totalDiscount,
          isIncluded: formData.isIncluded
        }),
      })

      if (response.ok) {
        await fetchFeatures()
        setIsFormOpen(false)
        setEditingFeature(null)
        resetForm()
      }
    } catch (error) {
      console.error('Error saving feature:', error)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      cost: 0,
      discountRate: 0,
      totalDiscount: 0,
      isIncluded: true
    })
  }

  const handleEdit = (feature: OptionFeature) => {
    setEditingFeature(feature)
    setFormData({
      name: feature.name,
      description: feature.description || '',
      cost: feature.cost || 0,
      discountRate: feature.discountRate || 0,
      totalDiscount: feature.totalDiscount || 0,
      isIncluded: feature.isIncluded
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (feature: OptionFeature) => {
    if (!confirm(`Are you sure you want to delete "${feature.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/service-option-features/${feature.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchFeatures()
      }
    } catch (error) {
      console.error('Error deleting feature:', error)
    }
  }

  const toggleIncluded = async (feature: OptionFeature) => {
    try {
      const response = await fetch(`/api/admin/service-option-features/${feature.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isIncluded: !feature.isIncluded
        }),
      })

      if (response.ok) {
        await fetchFeatures()
      }
    } catch (error) {
      console.error('Error updating feature:', error)
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Features for "{option.name}"
          </h2>
          <p className="text-gray-600">Manage individual features and their properties</p>
        </div>
        <button
          onClick={() => {
            setEditingFeature(null)
            resetForm()
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Feature</span>
        </button>
      </div>

      {/* Features List */}
      <div className="space-y-3">
        {features.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <StarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No features found for this option. Create your first feature to get started.</p>
          </div>
        ) : (
          features.map(feature => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1">
                  <button
                    onClick={() => toggleIncluded(feature)}
                    className={`p-1 rounded-full transition-colors ${
                      feature.isIncluded 
                        ? 'text-green-600 hover:bg-green-50' 
                        : 'text-red-600 hover:bg-red-50'
                    }`}
                  >
                    {feature.isIncluded ? (
                      <CheckCircleIcon className="h-6 w-6" />
                    ) : (
                      <XCircleIcon className="h-6 w-6" />
                    )}
                  </button>

                  <div className="p-2 bg-purple-100 rounded-lg">
                    <StarIcon className="h-5 w-5 text-purple-600" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {feature.name}
                        </h3>
                        {feature.description && (
                          <p className="text-sm text-gray-600">
                            {feature.description}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        {feature.cost && Number(feature.cost) > 0 && (
                          <div className="flex items-center space-x-1 text-sm text-gray-600">
                            <CurrencyDollarIcon className="h-4 w-4" />
                            <span>${Number(feature.cost)}</span>
                          </div>
                        )}
                        
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          feature.isIncluded 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {feature.isIncluded ? 'Included' : 'Optional'}
                        </span>
                        
                        {feature.discountRate && Number(feature.discountRate) > 0 && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {Number(feature.discountRate)}% off
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-1 ml-4">
                  <button
                    onClick={() => handleEdit(feature)}
                    className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(feature)}
                    className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Summary */}
      {features.length > 0 && (
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Features:</span>
              <span className="ml-2 font-semibold">{features.length}</span>
            </div>
            <div>
              <span className="text-gray-600">Included:</span>
              <span className="ml-2 font-semibold text-green-600">
                {features.filter(f => f.isIncluded).length}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Optional:</span>
              <span className="ml-2 font-semibold text-red-600">
                {features.filter(f => !f.isIncluded).length}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-lg mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                {editingFeature ? 'Edit Feature' : 'Add New Feature'}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Feature Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Cost ($)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.cost}
                      onChange={(e) => setFormData({ ...formData, cost: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Discount Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.isIncluded}
                      onChange={(e) => setFormData({ ...formData, isIncluded: e.target.checked })}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Included by default
                    </span>
                  </label>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    {editingFeature ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
