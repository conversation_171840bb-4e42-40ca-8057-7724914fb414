// Data transformation utilities to handle field name mismatches between validation schemas and database schema

// Transform camelCase validation data to database field names
export const transformToDbFields = {
  category: (data: any) => ({
    categname: data.name,
    categdesc: data.description,
    parentid: data.parentId ? Number(data.parentId) : 0,
    isactive: data.isActive ?? true,
    displayorder: data.displayOrder ?? 0,
  }),

  service: (data: any) => ({
    categid: Number(data.categoryId),
    name: data.name,
    description: data.description,
    iconclass: data.iconClass || 'fas fa-cog',
    price: data.price ? Number(data.price) : 0,
    discountrate: data.discountRate ?? 0,
    manager: data.manager || 'Admin',
    isactive: data.isActive ?? true,
    displayorder: data.displayOrder ?? 0,
  }),

  teamMember: (data: any) => ({
    name: data.name,
    position: data.position,
    birthdate: data.birthDate ? new Date(data.birthDate) : null,
    gender: data.gender,
    maritalstatus: data.maritalStatus,
    socialsecurityno: data.socialSecurityNo,
    hiredate: data.hireDate ? new Date(data.hireDate) : null,
    address: data.address,
    city: data.city,
    state: data.state,
    zipcode: data.zipCode,
    country: data.country,
    phone: data.phone,
    salary: data.salary ? Number(data.salary) : null,
    payrollmethod: data.payrollMethod,
    empresumeurl: data.resumeUrl,
    notes: data.notes,
    bio: data.bio,
    photourl: data.photoUrl,
    email: data.email,
    linkedinurl: data.linkedinUrl,
    twitterurl: data.twitterUrl,
    githuburl: data.githubUrl,
    displayorder: data.displayOrder ?? 0,
    isactive: data.isActive ?? true,
  }),

  technology: (data: any) => ({
    name: data.name,
    description: data.description,
    iconurl: data.iconUrl,
    displayorder: data.displayOrder ?? 0,
    isactive: data.isActive ?? true,
  }),

  blogPost: (data: any) => ({
    authorid: data.authorId,
    title: data.title,
    content: data.content,
    slug: data.slug,
    featuredimageurl: data.featuredImageUrl,
    excerpt: data.excerpt,
    ispublished: data.isPublished ?? false,
    publishedat: data.publishedAt ? new Date(data.publishedAt) : null,
    categories: data.categories,
    tags: data.tags,
  }),

  contactForm: (data: any) => ({
    name: data.name,
    email: data.email,
    phone: data.phone,
    subject: data.subject,
    message: data.message,
    isread: data.isRead ?? false,
    readat: data.readAt ? new Date(data.readAt) : null,
    status: data.status || 'New',
  }),

  jobListing: (data: any) => ({
    title: data.title,
    description: data.description,
    requirements: data.requirements,
    location: data.location,
    employmenttype: data.employmentType,
    salarymin: data.salaryMin ? Number(data.salaryMin) : null,
    salarymax: data.salaryMax ? Number(data.salaryMax) : null,
    salarycurrency: data.salaryCurrency || 'USD',
    isremote: data.isRemote ?? false,
    isactive: data.isActive ?? true,
    expiresat: data.expiresAt ? new Date(data.expiresAt) : null,
  }),

  heroSection: (data: any) => ({
    title: data.title,
    metadescription: data.metaDescription,
    metakeywords: data.metaKeywords,
    pagename: data.pageName,
    maintitle: data.mainTitle,
    mainsubtitle: data.mainSubtitle,
    maindescription: data.mainDescription,
    primarybuttontext: data.primaryButtonText,
    primarybuttonurl: data.primaryButtonUrl,
    secondarybuttontext: data.secondaryButtonText,
    secondarybuttonurl: data.secondaryButtonUrl,
    enableslideshow: data.enableSlideshow ? 1 : 0,
    slideshowspeed: data.slideshowSpeed ?? 5000,
    autoplay: data.autoplay ? 1 : 0,
    showdots: data.showDots ? 1 : 0,
    showarrows: data.showArrows ? 1 : 0,
    enablefloatingelements: data.enableFloatingElements ? 1 : 0,
    floatingelementsconfig: data.floatingElementsConfig,
    isactive: data.isActive ?? true,
    modifiedby: data.modifiedBy,
  }),

  heroSlide: (data: any) => ({
    herosectionid: Number(data.heroSectionId),
    content: data.content,
    mediatype: data.mediaType ?? 'image',
    imageurl: data.imageUrl,
    videourl: data.videoUrl,
    mediaalt: data.mediaAlt,
    videoautoplay: data.videoAutoplay ? 1 : 0,
    videomuted: data.videoMuted ? 1 : 0,
    videoloop: data.videoLoop ? 1 : 0,
    videocontrols: data.videoControls ? 1 : 0,
    buttontext: data.buttonText,
    buttonurl: data.buttonUrl,
    displayorder: data.displayOrder ?? 0,
    isactive: data.isActive ?? true,
    animationtype: data.animationType ?? 'fade',
    duration: data.duration ?? 5000,
  }),

  aboutPage: (data: any) => ({
    title: data.title,
    subtitle: data.subtitle,
    content: data.content,
    missiontitle: data.missionTitle,
    missioncontent: data.missionContent,
    visiontitle: data.visionTitle,
    visioncontent: data.visionContent,
    valuestitle: data.valuesTitle,
    valuescontent: data.valuesContent,
    ctatitle: data.ctaTitle,
    ctasubtitle: data.ctaSubtitle,
    ctaprimarybuttontext: data.ctaPrimaryButtonText,
    ctaprimarybuttonurl: data.ctaPrimaryButtonUrl,
    ctasecondarybuttontext: data.ctaSecondaryButtonText,
    ctasecondarybuttonurl: data.ctaSecondaryButtonUrl,
    isactive: data.isActive ?? true,
    modifiedby: data.modifiedBy,
  }),

  legalPage: (data: any) => ({
    title: data.title,
    slug: data.slug,
    metadescription: data.metaDescription,
    content: data.content,
    displayorder: data.displayOrder ?? 0,
    isactive: data.isActive ?? true,
    lastmodified: data.lastModified ? new Date(data.lastModified) : new Date(),
    modifiedby: data.modifiedBy || 'admin',
  }),

  siteSetting: (data: any) => ({
    settingkey: data.settingKey,
    settingvalue: data.settingValue,
    settingtype: data.settingType || 'text',
    description: data.description,
    isactive: data.isActive ?? true,
  }),

  client: (data: any) => ({
    userid: data.userId ? Number(data.userId) : null,
    companyname: data.companyName,
    contactname: data.contactName,
    contactposition: data.contactPosition,
    contactemail: data.contactEmail,
    contactphone: data.contactPhone,
    contactfax: data.contactFax,
    companywebsite: data.website || data.companyWebsite,
    address: data.address,
    city: data.city,
    state: data.state,
    zipcode: data.zipCode,
    country: data.country,
    logourl: data.logoUrl,
    notes: data.notes,
    isactive: data.isActive ?? true,
  }),

  project: (data: any) => ({
    name: data.name,
    description: data.description,
    projgoals: data.goals,
    projmanager: data.managerId ? Number(data.managerId) : null,
    clientid: data.clientId ? Number(data.clientId) : null,
    orderid: data.orderId ? Number(data.orderId) : null,
    imageurl: data.imageUrl,
    projecturl: data.projectUrl,
    githuburl: data.githubUrl,
    tags: data.tags,
    projstartdate: data.startDate ? new Date(data.startDate) : null,
    projcompletiondate: data.completionDate ? new Date(data.completionDate) : null,
    estimatecost: data.estimatedCost ? Number(data.estimatedCost) : null,
    estimatetime: data.estimatedTime,
    estimateeffort: data.estimatedEffort,
    status: data.status,
    isfeatured: data.isFeatured ?? false,
    ispublic: data.isPublic ?? true,
    displayorder: data.displayOrder ?? 0,
  }),

  user: (data: any) => ({
    email: data.email,
    password: data.password,
    firstname: data.firstName,
    lastname: data.lastName,
    role: data.role,
    imageurl: data.imageUrl,
    emailverified: data.emailVerified ? new Date(data.emailVerified) : null,
  }),

  testimonial: (data: any) => ({
    clientid: Number(data.clientId),
    clientname: data.clientName,
    clienttitle: data.clientTitle,
    clientcompany: data.clientCompany,
    clientphotourl: data.clientPhotoUrl,
    content: data.content,
    rating: Number(data.rating),
    isfeatured: data.isFeatured ?? false,
    displayorder: data.displayOrder ?? 0,
  }),

  technology: (data: any) => ({
    name: data.name,
    description: data.description,
    iconurl: data.iconUrl,
    displayorder: data.displayOrder ?? 0,
    isactive: data.isActive ?? true,
  }),

  order: (data: any) => ({
    ordertitle: data.title,
    clientid: Number(data.clientId),
    ordermanager: data.managerId ? Number(data.managerId) : null,
    orderdesc: data.description,
    orderdate: data.orderDate ? new Date(data.orderDate) : new Date(),
    ordertotalamount: data.totalAmount ? Number(data.totalAmount) : 0,
    orderdiscountrate: data.discountRate ?? 0,
    status: data.status,
    notes: data.notes,
    isactive: data.isActive ?? true,
  }),

  invoice: (data: any) => ({
    duedate: new Date(data.dueDate),
    subtotal: data.subtotal ? Number(data.subtotal) : 0,
    taxrate: data.taxRate ? Number(data.taxRate) : 0,
    taxamount: data.taxAmount ? Number(data.taxAmount) : 0,
    totalamount: data.totalAmount ? Number(data.totalAmount) : 0,
    status: data.status,
    description: data.description,
    clientid: Number(data.clientId),
    contid: data.contractId ? Number(data.contractId) : null,
    orderid: data.orderId ? Number(data.orderId) : null,
    projectid: data.projectId ? Number(data.projectId) : null,
    paidat: data.paidAt ? new Date(data.paidAt) : null,
  }),
}

// Transform database fields back to camelCase for API responses
export const transformFromDbFields = {
  category: (data: any) => ({
    id: data.id,
    name: data.categname,
    description: data.categdesc,
    parentId: data.parentid,
    isActive: data.isactive,
    displayOrder: data.displayorder,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
  }),

  service: (data: any) => ({
    id: Number(data.id),
    categoryId: Number(data.categid),
    name: data.name,
    description: data.description,
    iconClass: data.iconclass,
    price: data.price,
    discountRate: data.discountrate,
    manager: data.manager,
    isActive: data.isactive,
    displayOrder: data.displayorder,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    category: data.categories ? {
      id: Number(data.categories.id),
      name: data.categories.categname,
    } : null,
    serviceOptions: data.serviceoptions ? data.serviceoptions.map((opt: any) => ({
      id: Number(opt.id),
      name: opt.optname,
      price: opt.optprice,
    })) : [],
    _count: data._count ? {
      orderDetails: data._count.orderdetails || 0,
      serviceOptions: data._count.serviceoptions || 0,
    } : null,
  }),

  client: (data: any) => ({
    id: Number(data.id),
    userId: data.userid ? Number(data.userid) : null,
    companyName: data.companyname,
    contactName: data.contactname,
    contactPosition: data.contactposition,
    contactEmail: data.contactemail,
    contactPhone: data.contactphone,
    contactFax: data.contactfax,
    website: data.companywebsite,
    companyWebsite: data.companywebsite, // Keep both for compatibility
    address: data.address,
    city: data.city,
    state: data.state,
    zipCode: data.zipcode,
    country: data.country,
    logoUrl: data.logourl,
    isActive: data.isactive,
    notes: data.notes,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    _count: data._count ? {
      projects: data._count.projects || 0,
      contracts: data._count.contracts || 0,
      invoices: data._count.invoices || 0,
      orders: data._count.orders || 0,
      testimonials: data._count.testimonials || 0,
    } : null,
    projects: data.projects ? data.projects.map((project: any) => ({
      id: Number(project.id),
      name: project.name,
      description: project.description,
      status: project.status,
      startDate: project.projstartdate,
      completionDate: project.projcompletiondate,
      estimatedCost: Number(project.estimatecost || 0),
      createdAt: project.createdat,
    })) : [],
    contracts: data.contracts ? data.contracts.map((contract: any) => ({
      id: Number(contract.id),
      name: contract.contname,
      status: contract.contstatus,
      value: Number(contract.contvalue || 0),
      serviceType: contract.contservtype,
      createdAt: contract.createdat,
    })) : [],
    invoices: data.invoices ? data.invoices.map((invoice: any) => ({
      id: Number(invoice.id),
      invoiceNumber: invoice.invoicenumber,
      totalAmount: Number(invoice.totalamount || 0),
      status: invoice.status,
      dueDate: invoice.duedate,
      paidAt: invoice.paidat,
      createdAt: invoice.createdat,
    })) : [],
    orders: data.orders ? data.orders.map((order: any) => ({
      id: Number(order.id),
      title: order.ordertitle,
      totalAmount: Number(order.ordertotalamount || 0),
      status: order.status,
      orderDate: order.orderdate,
      createdAt: order.createdat,
    })) : [],
    testimonials: data.testimonials ? data.testimonials.map((testimonial: any) => ({
      id: Number(testimonial.id),
      clientName: testimonial.clientname,
      clientTitle: testimonial.clienttitle,
      clientCompany: testimonial.clientcompany,
      content: testimonial.content,
      rating: testimonial.rating,
      isFeatured: testimonial.isfeatured,
      createdAt: testimonial.createdat,
    })) : [],
  }),

  project: (data: any) => ({
    id: Number(data.id),
    name: data.name,
    description: data.description,
    goals: data.projgoals,
    managerId: data.projmanager ? Number(data.projmanager) : null,
    clientId: data.clientid ? Number(data.clientid) : null,
    orderId: data.orderid ? Number(data.orderid) : null,
    imageUrl: data.imageurl,
    projectUrl: data.projecturl,
    githubUrl: data.githuburl,
    tags: data.tags,
    startDate: data.projstartdate,
    completionDate: data.projcompletiondate,
    estimateCost: data.estimatecost,
    estimateTime: data.estimatetime,
    estimateEffort: data.estimateeffort,
    status: data.status,
    isFeatured: data.isfeatured,
    isPublic: data.ispublic,
    displayOrder: data.displayorder,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    client: data.clients ? {
      id: Number(data.clients.id),
      companyName: data.clients.companyname,
    } : null,
    feedbacks: data.feedbacks || [],
  }),

  teamMember: (data: any) => ({
    id: Number(data.id),
    name: data.name,
    position: data.position,
    birthDate: data.birthdate,
    gender: data.gender,
    maritalStatus: data.maritalstatus,
    socialSecurityNo: data.socialsecurityno,
    hireDate: data.hiredate,
    address: data.address,
    city: data.city,
    state: data.state,
    zipCode: data.zipcode,
    country: data.country,
    phone: data.phone,
    salary: data.salary,
    payrollMethod: data.payrollmethod,
    resumeUrl: data.empresumeurl,
    notes: data.notes,
    bio: data.bio,
    photoUrl: data.photourl,
    email: data.email,
    linkedinUrl: data.linkedinurl,
    twitterUrl: data.twitterurl,
    githubUrl: data.githuburl,
    displayOrder: data.displayorder,
    isActive: data.isactive,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    projects: data.projects ? data.projects.map((project: any) => ({
      id: Number(project.id),
      name: project.name,
      status: project.status,
    })) : [],
    payrollRecords: data.payrollrecords ? data.payrollrecords.map((record: any) => ({
      id: Number(record.id),
      payDate: record.paydate,
      grossPay: record.grosspay,
    })) : [],
    tasks: data.tasks ? data.tasks.map((task: any) => ({
      id: Number(task.id),
      taskDesc: task.taskdesc,
      status: task.status,
    })) : [],
    _count: data._count ? {
      projects: data._count.projects || 0,
      payrollRecords: data._count.payrollrecords || 0,
      tasks: data._count.tasks || 0,
    } : null,
  }),

  technology: (data: any) => ({
    id: Number(data.id),
    name: data.name,
    description: data.description,
    iconUrl: data.iconurl,
    displayOrder: data.displayorder,
    isActive: data.isactive,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    _count: data._count ? {
      projectTechnologies: data._count.projecttechnologies || 0,
    } : null,
  }),

  testimonial: (data: any) => ({
    id: Number(data.id),
    clientName: data.clientname,
    clientTitle: data.clienttitle,
    clientCompany: data.clientcompany,
    clientPhotoUrl: data.clientphotourl,
    content: data.content,
    rating: data.rating,
    projectId: data.projectid ? Number(data.projectid) : null,
    clientId: data.clientid ? Number(data.clientid) : null,
    isPublic: data.ispublic,
    isFeatured: data.isfeatured,
    displayOrder: data.displayorder,
    isActive: data.isactive,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
  }),

  blogPost: (data: any) => ({
    id: Number(data.id),
    authorId: data.authorid ? Number(data.authorid) : null,
    title: data.title,
    content: data.content,
    slug: data.slug,
    featuredImageUrl: data.featuredimageurl,
    excerpt: data.excerpt,
    isPublished: data.ispublished,
    publishedAt: data.publishedat,
    categories: data.categories,
    tags: data.tags,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    author: data.author ? {
      id: Number(data.author.id),
      firstName: data.author.firstname,
      lastName: data.author.lastname,
      email: data.author.email,
    } : null,
  }),

  heroSection: (data: any) => ({
    id: Number(data.id),
    title: data.title,
    metaDescription: data.metadescription,
    metaKeywords: data.metakeywords,
    pageName: data.pagename,
    mainTitle: data.maintitle,
    mainSubtitle: data.mainsubtitle,
    mainDescription: data.maindescription,
    primaryButtonText: data.primarybuttontext,
    primaryButtonUrl: data.primarybuttonurl,
    secondaryButtonText: data.secondarybuttontext,
    secondaryButtonUrl: data.secondarybuttonurl,
    enableSlideshow: Boolean(data.enableslideshow),
    slideshowSpeed: data.slideshowspeed,
    autoplay: Boolean(data.autoplay),
    showDots: Boolean(data.showdots),
    showArrows: Boolean(data.showarrows),
    enableFloatingElements: Boolean(data.enablefloatingelements),
    floatingElementsConfig: data.floatingelementsconfig,
    isActive: data.isactive,
    modifiedBy: data.modifiedby,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    slides: data.heroslides ? data.heroslides.map((slide: any) => transformFromDbFields.heroSlide(slide)) : [],
  }),

  heroSlide: (data: any) => ({
    id: Number(data.id),
    heroSectionId: Number(data.herosectionid),
    content: data.content,
    mediaType: data.mediatype,
    imageUrl: data.imageurl,
    videoUrl: data.videourl,
    mediaAlt: data.mediaalt,
    videoAutoplay: Boolean(data.videoautoplay),
    videoMuted: Boolean(data.videomuted),
    videoLoop: Boolean(data.videoloop),
    videoControls: Boolean(data.videocontrols),
    buttonText: data.buttontext,
    buttonUrl: data.buttonurl,
    displayOrder: data.displayorder,
    isActive: data.isactive,
    animationType: data.animationtype,
    duration: data.duration,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
  }),

  user: (data: any) => ({
    id: Number(data.id),
    email: data.email,
    emailVerified: data.emailverified,
    firstName: data.firstname,
    lastName: data.lastname,
    imageUrl: data.imageurl,
    role: data.role,
    isActive: data.isactive,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
  }),

  legalPage: (data: any) => ({
    id: Number(data.id),
    title: data.title,
    slug: data.slug,
    metaDescription: data.metadescription,
    content: data.content,
    isActive: data.isactive,
    displayOrder: data.displayorder,
    lastModified: data.lastmodified,
    modifiedBy: data.modifiedby,
    createdAt: data.createdat,
    updatedAt: data.updatedat,
    sections: data.legalpagesections ? data.legalpagesections.map((section: any) => ({
      id: Number(section.id),
      legalPageId: Number(section.legalpageid),
      title: section.title,
      content: section.content,
      iconClass: section.iconclass,
      displayOrder: section.displayorder,
      isActive: section.isactive,
      createdAt: section.createdat,
      updatedAt: section.updatedat,
    })) : [],
  }),
}

// Helper function to convert string IDs to numbers for BigInt fields
export const convertIds = (data: any, idFields: string[]) => {
  const converted = { ...data }
  idFields.forEach(field => {
    if (converted[field] && typeof converted[field] === 'string') {
      converted[field] = Number(converted[field])
    }
  })
  return converted
}
